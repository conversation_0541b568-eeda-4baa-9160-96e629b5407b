import 'package:emoji_rating_bar/emoji_rating_bar.dart';
import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../../controllers/mood_controller.dart';

class AddMoodScreen extends StatefulWidget {
  final String? dateParam;

  const AddMoodScreen({super.key, this.dateParam});

  @override
  State<AddMoodScreen> createState() => _AddMoodScreenState();
}

class _AddMoodScreenState extends State<AddMoodScreen> {
  final note = TextEditingController();

  bool isSharedWithAI = false;

  double rating = 1;

  DateTime? selectedDate;

  @override
  void initState() {
    note.clear();

    // Parse the date parameter if provided
    if (widget.dateParam != null) {
      try {
        final timestamp = int.parse(widget.dateParam!);
        selectedDate = DateTime.fromMillisecondsSinceEpoch(timestamp);
      } catch (e) {
        selectedDate = null;
      }
    }

    super.initState();
  }

  @override
  void dispose() {
    note.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          selectedDate != null
              ? 'Add Mood - ${selectedDate!.day}/${selectedDate!.month}/${selectedDate!.year}'
              : 'Add Mood',
        ),
        elevation: 0,
      ),
      body: ListView(
        padding: EdgeInsets.symmetric(horizontal: 16.0),
        children: [
          const SizedBox(height: 20),
          Center(
            child: EmojiRatingBar(
              list: [
                EmojiData('assets/images/mad.png', "Mad"),
                EmojiData('assets/images/bad.png', "Bad"),
                EmojiData('assets/images/ok.png', "OK"),
                EmojiData('assets/images/happy.png', "Happy"),
                EmojiData('assets/images/joy.png', "Joy"),
              ],
              ratingBarType: RatingBarType.mood,
              rating: 0,
              onRateChange: (ratingChange) {
                //di<MoodController>().updateMood(rating);
                setState(() {
                  rating = ratingChange;
                });
              },
              isReadOnly: false,
              spacing: 10,
              size: 50,
              selectedSize: 65,
              isShowTitle: true,
              isShowDivider: false,
              titleStyle: const TextStyle(color: Colors.grey, fontSize: 14),
              selectedTitleStyle: TextStyle(
                color:
                    Theme.of(context).brightness == Brightness.light
                        ? const Color(0xFF2D2D2D)
                        : const Color(0xFFF8F9FA),
                fontSize: 12,
              ),
              animationDuration: const Duration(milliseconds: 500),
              animationCurve: Curves.easeInOut,
              applyColorFilter: true,
            ),
          ),
          SizedBox(height: 20),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: TextField(
              maxLength: 40,
              controller: note,
              decoration: InputDecoration(
                labelText: 'Note',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ),
          SizedBox(height: 20),
          //add toggle
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                Text("Share with AI", style: TextStyle(fontSize: 18)),
                SizedBox(width: 10),
                Switch(
                  value: isSharedWithAI,
                  onChanged: (value) {
                    setState(() {
                      isSharedWithAI = value;
                    });
                  },
                ),
              ],
            ),
          ),
          SizedBox(height: 40),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: ElevatedButton(
              onPressed: () async {
                FocusManager.instance.primaryFocus?.unfocus();
                await di<MoodController>().addMood(
                  note.text.trim(),
                  isSharedWithAI,
                  rating,
                  selectedDate: selectedDate,
                );
              },
              child: const Text("Save"),
            ),
          ),
        ],
      ),
    );
  }
}
