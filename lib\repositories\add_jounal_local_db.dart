import 'package:moodvibe/models/jounal.dart';
import 'package:objectbox/objectbox.dart';

import 'i_add_jounal_local_db.dart';

class AddJournalLocalDb implements IAddJournalLocalDb {
  final Box<JournalModel> _box;

  AddJournalLocalDb(this._box);

  @override
  Future<void> saveJournal(JournalModel model) async {
    _box.put(model);
  }

  @override
  Future<List<JournalModel>> getJournals() async {
    return _box.getAll();
  }

  @override
  Future<void> deleteJournal(int id) async {
    _box.remove(id);
  }
}
