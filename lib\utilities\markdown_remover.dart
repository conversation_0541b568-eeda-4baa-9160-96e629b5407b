/// Utility class to remove markdown formatting from text
class MarkdownRemover {
  /// Remove all markdown formatting from text and return clean text
  static String removeMarkdown(String text) {
    if (text.isEmpty) return text;

    String cleanText = text;

    // Remove code blocks first (```text```) - do this before other processing
    cleanText = cleanText.replaceAll(RegExp(r'```[\s\S]*?```'), '');

    // Remove headers (# ## ### #### ##### ######)
    cleanText = cleanText.replaceAllMapped(
      RegExp(r'^#{1,6}\s+(.*)$', multiLine: true),
      (match) => match.group(1) ?? '',
    );

    // Remove bold (**text** or __text__)
    cleanText = cleanText.replaceAllMapped(
      RegExp(r'\*\*(.*?)\*\*'),
      (match) => match.group(1) ?? '',
    );
    cleanText = cleanText.replaceAllMapped(
      RegExp(r'__(.*?)__'),
      (match) => match.group(1) ?? '',
    );

    // Remove italic (*text* or _text_) - simple approach to avoid lookbehind issues
    cleanText = cleanText.replaceAllMapped(
      RegExp(r'\*([^*\n]+)\*'),
      (match) => match.group(1) ?? '',
    );
    cleanText = cleanText.replaceAllMapped(
      RegExp(r'_([^_\n]+)_'),
      (match) => match.group(1) ?? '',
    );

    // Remove strikethrough (~~text~~)
    cleanText = cleanText.replaceAllMapped(
      RegExp(r'~~(.*?)~~'),
      (match) => match.group(1) ?? '',
    );

    // Remove inline code (`text`)
    cleanText = cleanText.replaceAllMapped(
      RegExp(r'`([^`]+)`'),
      (match) => match.group(1) ?? '',
    );

    // Remove links [text](url)
    cleanText = cleanText.replaceAllMapped(
      RegExp(r'\[([^\]]+)\]\([^\)]+\)'),
      (match) => match.group(1) ?? '',
    );

    // Remove images ![alt](url)
    cleanText = cleanText.replaceAllMapped(
      RegExp(r'!\[([^\]]*)\]\([^\)]+\)'),
      (match) => match.group(1) ?? '',
    );

    // Remove horizontal rules (--- or ***)
    cleanText = cleanText.replaceAll(
      RegExp(r'^[-*]{3,}$', multiLine: true),
      '',
    );

    // Remove blockquotes (> text)
    cleanText = cleanText.replaceAllMapped(
      RegExp(r'^>\s+(.*)$', multiLine: true),
      (match) => match.group(1) ?? '',
    );

    // Remove unordered list markers (- * +)
    cleanText = cleanText.replaceAllMapped(
      RegExp(r'^[\s]*[-*+]\s+(.*)$', multiLine: true),
      (match) => match.group(1) ?? '',
    );

    // Remove ordered list markers (1. 2. etc.)
    cleanText = cleanText.replaceAllMapped(
      RegExp(r'^[\s]*\d+\.\s+(.*)$', multiLine: true),
      (match) => match.group(1) ?? '',
    );

    // Remove table formatting (| text |)
    cleanText = cleanText.replaceAll(RegExp(r'\|'), '');
    cleanText = cleanText.replaceAll(RegExp(r'^[-\s:]+$', multiLine: true), '');

    // Remove HTML tags if any
    cleanText = cleanText.replaceAll(RegExp(r'<[^>]*>'), '');

    // Clean up extra whitespace
    cleanText = cleanText.replaceAll(RegExp(r'\n\s*\n'), '\n\n');
    cleanText = cleanText.replaceAll(RegExp(r'[ \t]+'), ' ');
    cleanText = cleanText.trim();

    return cleanText;
  }

  /// Remove only basic markdown formatting (bold, italic, code)
  static String removeBasicMarkdown(String text) {
    if (text.isEmpty) return text;

    String cleanText = text;

    // Remove bold (**text** or __text__)
    cleanText = cleanText.replaceAll(RegExp(r'\*\*(.*?)\*\*'), r'$1');
    cleanText = cleanText.replaceAll(RegExp(r'__(.*?)__'), r'$1');

    // Remove italic (*text* or _text_)
    cleanText = cleanText.replaceAll(RegExp(r'\*(.*?)\*'), r'$1');
    cleanText = cleanText.replaceAll(RegExp(r'_(.*?)_'), r'$1');

    // Remove inline code (`text`)
    cleanText = cleanText.replaceAll(RegExp(r'`([^`]+)`'), r'$1');

    return cleanText.trim();
  }
}
