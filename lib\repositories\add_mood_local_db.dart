import 'package:moodvibe/objectbox.g.dart';

import '../models/mood.dart';
import 'i_add_mood_local_db.dart';

class AddMoodLocalDb implements IAddMoodLocalDb {
  final Box<MoodModel> _box;

  AddMoodLocalDb(this._box);

  @override
  Future<void> saveMood(MoodModel model) async {
    _box.put(model);
  }

  @override
  Future<List<MoodModel>> getMoods() async {
    return _box.getAll();
  }

  @override
  Future<void> deleteMood(int id) async {
    _box.remove(id);
  }
}
