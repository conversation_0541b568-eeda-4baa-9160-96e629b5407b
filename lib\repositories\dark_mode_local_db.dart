import 'package:shared_preferences/shared_preferences.dart';

import 'i_dark_mode_local_db.dart';

class DarkModeLocalDb implements IDarkModeLocalDb {
  @override
  Future<void> savePreference(bool mode) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final currentMode = prefs.getBool('darkMode') ?? false;
    await prefs.setBool('darkMode', !currentMode);
  }

  @override
  Future<bool> loadPreference() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool('darkMode') ?? false;
  }
}
