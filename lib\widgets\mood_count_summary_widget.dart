import 'package:flutter/material.dart';

import '../models/mood.dart';
import '../theme/app_theme.dart';

class MoodCountSummaryWidget extends StatelessWidget {
  final List<MoodModel> moods;

  const MoodCountSummaryWidget({
    super.key,
    required this.moods,
  });

  @override
  Widget build(BuildContext context) {
    final moodCounts = _calculateMoodCounts();
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.citrus.withValues(alpha: 0.1),
            AppTheme.appleCore.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.blueberry.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'All Time Mood Summary',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.blueberry,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildMoodItem(
                context,
                'Mad',
                'assets/images/mad.png',
                moodCounts['Mad'] ?? 0,
                const Color(0xFFFF0000),
              ),
              _buildMoodItem(
                context,
                'Bad',
                'assets/images/bad.png',
                moodCounts['Bad'] ?? 0,
                const Color(0xFFFF5600),
              ),
              _buildMoodItem(
                context,
                'OK',
                'assets/images/ok.png',
                moodCounts['OK'] ?? 0,
                const Color(0xFF0070FF),
              ),
              _buildMoodItem(
                context,
                'Happy',
                'assets/images/happy.png',
                moodCounts['Happy'] ?? 0,
                const Color(0xFFFFC800),
              ),
              _buildMoodItem(
                context,
                'Joy',
                'assets/images/joy.png',
                moodCounts['Joy'] ?? 0,
                const Color(0xFF25FF2E),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildTotalCount(context),
        ],
      ),
    );
  }

  Widget _buildMoodItem(
    BuildContext context,
    String moodName,
    String imagePath,
    int count,
    Color moodColor,
  ) {
    return Expanded(
      child: Column(
        children: [
          // Count above image
          Container(
            width: 32,
            height: 24,
            decoration: BoxDecoration(
              color: moodColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: moodColor.withValues(alpha: 0.4),
                width: 1,
              ),
            ),
            child: Center(
              child: Text(
                count.toString(),
                style: TextStyle(
                  color: moodColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ),
          ),
          const SizedBox(height: 8),
          
          // Mood image
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: moodColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: moodColor.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(11),
              child: Image.asset(
                imagePath,
                width: 40,
                height: 40,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(
                    Icons.mood,
                    color: moodColor,
                    size: 30,
                  );
                },
              ),
            ),
          ),
          const SizedBox(height: 8),
          
          // Mood name below image
          Text(
            moodName,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: moodColor,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalCount(BuildContext context) {
    final totalMoods = moods.length;
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: AppTheme.blueberry.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.blueberry.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics_outlined,
            color: AppTheme.blueberry,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            'Total Moods Tracked: ',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.blueberry.withValues(alpha: 0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            totalMoods.toString(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.blueberry,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Map<String, int> _calculateMoodCounts() {
    final counts = <String, int>{
      'Mad': 0,
      'Bad': 0,
      'OK': 0,
      'Happy': 0,
      'Joy': 0,
    };

    for (final mood in moods) {
      final moodName = mood.mood ?? 'OK';
      if (counts.containsKey(moodName)) {
        counts[moodName] = counts[moodName]! + 1;
      }
    }

    return counts;
  }
}
