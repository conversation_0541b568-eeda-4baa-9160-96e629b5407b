import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../models/mood.dart';
import '../theme/app_theme.dart';
import 'mood_chart_empty_state_widget.dart';

class MoodLineChartWidget extends StatelessWidget {
  final List<MoodModel> moods;
  final int daysToShow;

  const MoodLineChartWidget({
    super.key,
    required this.moods,
    this.daysToShow = 10,
  });

  @override
  Widget build(BuildContext context) {
    final chartData = _prepareChartData();

    // Check if there are any actual mood entries (not just default values)
    final hasRealMoods = chartData.any((dataPoint) => dataPoint.moodCount > 0);

    // If no real moods, show the empty state widget
    if (!hasRealMoods) {
      return const MoodChartEmptyStateWidget();
    }

    // Otherwise show the chart
    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.citrus.withValues(alpha: 0.1),
            AppTheme.appleCore.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.blueberry.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Mood Trends (Last $daysToShow Days)',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.blueberry,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: LineChart(
              LineChartData(
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: true,
                  drawHorizontalLine: true,
                  horizontalInterval: 1,
                  verticalInterval: 1,
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: AppTheme.blueberry.withValues(alpha: 0.1),
                      strokeWidth: 1,
                    );
                  },
                  getDrawingVerticalLine: (value) {
                    return FlLine(
                      color: AppTheme.blueberry.withValues(alpha: 0.1),
                      strokeWidth: 1,
                    );
                  },
                ),
                titlesData: FlTitlesData(
                  show: true,
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 30,
                      interval: 1,
                      getTitlesWidget: (value, meta) {
                        final dayIndex = value.toInt();
                        if (dayIndex >= 0 && dayIndex < chartData.length) {
                          final date = chartData[dayIndex].date;
                          return Text(
                            date.day.toString(),
                            style: TextStyle(
                              color: AppTheme.blueberry.withValues(alpha: 0.7),
                              fontWeight: FontWeight.w500,
                              fontSize: 12,
                            ),
                          );
                        }
                        return const Text('');
                      },
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      interval: 1,
                      reservedSize: 60,
                      getTitlesWidget: (value, meta) {
                        final moodNames = {
                          1.0: 'Mad',
                          2.0: 'Bad',
                          3.0: 'OK',
                          4.0: 'Happy',
                          5.0: 'Joy',
                        };

                        if (moodNames.containsKey(value)) {
                          final moodName = moodNames[value]!;
                          return Text(
                            moodName,
                            style: TextStyle(
                              color: _getMoodColor(moodName),
                              fontWeight: FontWeight.w600,
                              fontSize: 11,
                            ),
                          );
                        }
                        return const Text('');
                      },
                    ),
                  ),
                ),
                borderData: FlBorderData(
                  show: true,
                  border: Border.all(
                    color: AppTheme.blueberry.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                minX: 0,
                maxX: (daysToShow - 1).toDouble(),
                minY: 0.5,
                maxY: 5.2,
                lineBarsData: [
                  LineChartBarData(
                    spots: _generateSpots(chartData),
                    isCurved: true,
                    gradient: LinearGradient(
                      colors: [AppTheme.apricot, AppTheme.citrus],
                    ),
                    barWidth: 3,
                    isStrokeCapRound: true,
                    dotData: FlDotData(
                      show: true,
                      getDotPainter: (spot, percent, barData, index) {
                        final moodValue = spot.y;
                        return FlDotCirclePainter(
                          radius: 6,
                          color: _getMoodColorFromRating(moodValue),
                          strokeWidth: 2,
                          strokeColor: Colors.white,
                        );
                      },
                    ),
                    belowBarData: BarAreaData(
                      show: true,
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          AppTheme.citrus.withValues(alpha: 0.3),
                          AppTheme.citrus.withValues(alpha: 0.1),
                        ],
                      ),
                    ),
                  ),
                ],
                lineTouchData: LineTouchData(
                  enabled: true,
                  touchTooltipData: LineTouchTooltipData(
                    getTooltipColor: (touchedSpot) => AppTheme.blueberry,
                    getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                      return touchedBarSpots.map((barSpot) {
                        final dayIndex = barSpot.x.toInt();
                        final moodRating = barSpot.y;
                        final moodName = _getMoodNameFromRating(moodRating);

                        if (dayIndex < chartData.length) {
                          final date = chartData[dayIndex].date;
                          final formattedDate = DateFormat(
                            'MMM d',
                          ).format(date);

                          return LineTooltipItem(
                            '$formattedDate\n$moodName',
                            const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          );
                        }
                        return null;
                      }).toList();
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<ChartDataPoint> _prepareChartData() {
    final now = DateTime.now();
    final chartData = <ChartDataPoint>[];

    // Generate last N days
    for (int i = daysToShow - 1; i >= 0; i--) {
      final date = DateTime(now.year, now.month, now.day - i);

      // Find moods for this date
      final dayMoods =
          moods.where((mood) {
            final moodDate = mood.createdAt ?? DateTime.now();
            return moodDate.year == date.year &&
                moodDate.month == date.month &&
                moodDate.day == date.day;
          }).toList();

      // Calculate average mood for the day
      double averageRating = 3.0; // Default to "OK" if no moods
      if (dayMoods.isNotEmpty) {
        final totalRating = dayMoods.fold<double>(
          0.0,
          (sum, mood) => sum + (mood.rating ?? 3.0),
        );
        averageRating = totalRating / dayMoods.length;
      }

      chartData.add(
        ChartDataPoint(
          date: date,
          rating: averageRating,
          moodCount: dayMoods.length,
        ),
      );
    }

    return chartData;
  }

  List<FlSpot> _generateSpots(List<ChartDataPoint> chartData) {
    return chartData.asMap().entries.map((entry) {
      final index = entry.key;
      final dataPoint = entry.value;
      return FlSpot(index.toDouble(), dataPoint.rating);
    }).toList();
  }

  Color _getMoodColor(String mood) {
    switch (mood.toLowerCase()) {
      case 'mad':
        return const Color(0xFFFF0000);
      case 'bad':
        return const Color(0xFFFF5600);
      case 'ok':
        return const Color(0xFF0070FF);
      case 'happy':
        return const Color(0xFFFFC800);
      case 'joy':
        return const Color(0xFF25FF2E);
      default:
        return AppTheme.blueberry;
    }
  }

  Color _getMoodColorFromRating(double rating) {
    if (rating <= 1.5) return const Color(0xFFFF0000); // Mad
    if (rating <= 2.5) return const Color(0xFFFF5600); // Bad
    if (rating <= 3.5) return const Color(0xFF0070FF); // OK
    if (rating <= 4.5) return const Color(0xFFFFC800); // Happy
    return const Color(0xFF25FF2E); // Joy
  }

  String _getMoodNameFromRating(double rating) {
    if (rating <= 1.5) return 'Mad';
    if (rating <= 2.5) return 'Bad';
    if (rating <= 3.5) return 'OK';
    if (rating <= 4.5) return 'Happy';
    return 'Joy';
  }
}

class ChartDataPoint {
  final DateTime date;
  final double rating;
  final int moodCount;

  ChartDataPoint({
    required this.date,
    required this.rating,
    required this.moodCount,
  });
}
