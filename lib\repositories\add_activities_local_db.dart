import '../models/activities_model.dart';
import '../objectbox.g.dart';

class AddActivitiesLocalDb {
  final Box<ActivitiesModel> _box;

  AddActivitiesLocalDb(this._box);

  @override
  Future<void> saveActivity(ActivitiesModel model) async {
    _box.put(model);
  }

  @override
  Future<List<ActivitiesModel>> getActivities() async {
    return _box.getAll();
  }

  @override
  Future<void> deleteActivity(int id) async {
    _box.remove(id);
  }

  @override
  Future<void> deleteAllActivities() async {
    _box.removeAll();
  }
}
