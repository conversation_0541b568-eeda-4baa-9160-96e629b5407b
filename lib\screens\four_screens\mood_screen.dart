import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:moodvibe/theme/app_theme.dart';
import 'package:watch_it/watch_it.dart';

import '../../controllers/mood_controller.dart';
import '../../models/mood.dart';
import '../../theme/icon_size.dart';
import '../../widgets/mood_card_widget.dart';

class MoodScreen extends StatelessWidget with WatchItMixin {
  const MoodScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final moodController = watchIt<MoodController>();

    return Scaffold(
      backgroundColor:
          Theme.of(context).brightness == Brightness.light
              ? AppTheme.lightBackground
              : AppTheme.darkBackground,
      appBar: AppBar(
        title: const Text('My Moods'),
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {
              context.push('/calendarView');
            },
            icon: const Icon(
              Icons.calendar_month,
              size: AppIconSize.appIconSize,
            ),
          ),
          SizedBox(width: 5),
          IconButton(
            onPressed: () {
              context.push('/settings');
            },
            icon: const Icon(Icons.settings, size: AppIconSize.appIconSize),
          ),
        ],
      ),
      body:
          moodController.isLoading
              ? const Center(child: CircularProgressIndicator())
              : moodController.moods.isEmpty
              ? _buildEmptyState(context)
              : _buildMoodList(context, moodController.moods),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.push('/addMood');
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.mood,
            size: 80,
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No mood entries yet',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tap the + button to add your first mood',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMoodList(BuildContext context, List<MoodModel> moods) {
    // Sort moods by date (newest first)
    final sortedMoods = List<MoodModel>.from(moods)..sort(
      (a, b) => (b.createdAt ?? DateTime.now()).compareTo(
        a.createdAt ?? DateTime.now(),
      ),
    );

    return ListView.builder(
      padding: const EdgeInsets.only(top: 8, left: 8, right: 8, bottom: 80),
      itemCount: sortedMoods.length,
      itemBuilder: (context, index) {
        final mood = sortedMoods[index];
        return MoodCardWidget(mood: mood, showDismissible: true, index: index);
      },
    );
  }
}
