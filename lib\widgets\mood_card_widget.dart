import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/mood_controller.dart';
import '../models/mood.dart';
import '../theme/app_theme.dart';

class MoodCardWidget extends StatelessWidget {
  final MoodModel mood;
  final bool showDismissible;
  final int? index;

  const MoodCardWidget({
    super.key,
    required this.mood,
    this.showDismissible = true,
    this.index,
  });

  @override
  Widget build(BuildContext context) {
    final moodImage = _getMoodImage(mood.mood ?? 'OK');
    final moodColor = _getMoodColor(mood.mood ?? 'OK');
    final formattedDate = _formatDate(mood.createdAt);
    final formattedTime = _formatTime(mood.createdAt);

    final cardContent = Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Mood Image
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: moodColor.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: moodColor.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(11),
                child: Image.asset(
                  moodImage,
                  width: 40,
                  height: 40,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return Icon(Icons.mood, color: moodColor, size: 30);
                  },
                ),
              ),
            ),
            const SizedBox(width: 16),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Mood name and rating
                  Row(
                    children: [
                      Text(
                        mood.mood ?? 'Unknown',
                        style: Theme.of(
                          context,
                        ).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: moodColor,
                          fontSize: 20,
                        ),
                      ),
                      const SizedBox(width: 8),
                      if (mood.rating != null)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: moodColor.withValues(alpha: 0.5),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '${mood.rating!.toInt()}/5',
                            style: Theme.of(
                              context,
                            ).textTheme.bodySmall?.copyWith(
                              color:
                                  Theme.brightnessOf(context) ==
                                          Brightness.light
                                      ? Colors.black
                                      : Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                    ],
                  ),

                  const SizedBox(height: 4),

                  // Date and time
                  Text(
                    '$formattedDate at $formattedTime',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontSize: 16,
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.5),
                    ),
                  ),

                  // Note (if exists)
                  if (mood.note != null && mood.note!.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color:
                            Theme.of(context).brightness == Brightness.light
                                ? const Color(0xFFF8F9FA)
                                : const Color(0xFF2D2D2D),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        mood.note!,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontStyle: FontStyle.italic,
                          fontSize: 22,
                        ),
                      ),
                    ),
                  ],
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      if (mood.isSharedWithAI) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.citrus.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.psychology,
                                size: 16,
                                color: AppTheme.citrus,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'Shared with AI',
                                style: Theme.of(
                                  context,
                                ).textTheme.bodySmall?.copyWith(
                                  color: AppTheme.citrus,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );

    // Wrap with Dismissible only if showDismissible is true
    if (showDismissible) {
      return Dismissible(
        background: Container(
          color: AppTheme.citrus,
          alignment: Alignment.centerRight,
          padding: const EdgeInsets.only(right: 16, bottom: 0),
          child: const Icon(Icons.delete, color: Colors.white),
        ),
        key: Key(mood.id.toString()),
        direction: DismissDirection.endToStart,
        onDismissed: (direction) async {
          await di<MoodController>().deleteMood(mood.id);
        },
        child: cardContent,
      );
    }

    return cardContent;
  }

  String _getMoodImage(String mood) {
    switch (mood.toLowerCase()) {
      case 'mad':
        return 'assets/images/mad.png';
      case 'bad':
        return 'assets/images/bad.png';
      case 'ok':
        return 'assets/images/ok.png';
      case 'happy':
        return 'assets/images/happy.png';
      case 'joy':
        return 'assets/images/joy.png';
      default:
        return 'assets/images/ok.png';
    }
  }

  Color _getMoodColor(String mood) {
    switch (mood.toLowerCase()) {
      case 'mad':
        return const Color(0xFFFF0000);
      case 'bad':
        return const Color(0xFFFF5600);
      case 'ok':
        return const Color(0xFF0070FF);
      case 'happy':
        return const Color(0xFFFFC800);
      case 'joy':
        return const Color(0xFF25FF2E);
      default:
        return const Color(0xFF38B2AC);
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown date';

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);

    if (dateOnly == today) {
      return 'Today';
    } else if (dateOnly == yesterday) {
      return 'Yesterday';
    } else {
      return DateFormat('MMM d, y').format(date);
    }
  }

  String _formatTime(DateTime? date) {
    if (date == null) return '';
    return DateFormat('h:mm a').format(date);
  }
}
