import 'package:flutter/cupertino.dart';
import 'package:moodvibe/controllers/auth_controller.dart';
import 'package:moodvibe/theme/app_theme.dart';
import 'package:watch_it/watch_it.dart';

import '../models/mood.dart';
import '../repositories/i_add_mood_local_db.dart';

import '../repositories/i_backend_api.dart';
import '../services/analytics_service.dart';
import '../utilities/snack_bar_show.dart';

class MoodController extends ChangeNotifier {
  MoodController() {
    init();
  }

  List<MoodModel> _moods = [];

  List<MoodModel> get moods => _moods;

  List<MoodModel> _calenderMoods = [];

  List<MoodModel> get calenderMoods => _calenderMoods;

  double rating = 0;
  String mood = 'Mad';

  bool get isLoading => _isLoading;
  bool _isLoading = false;

  Future<void> init() async {
    await getMoods();
    await sortMoodsByDate(DateTime.now());
  }

  Future<void> getMoods() async {
    _isLoading = true;
    notifyListeners();
    _moods = await di<IAddMoodLocalDb>().getMoods();
    _isLoading = false;
    notifyListeners();
  }

  Future<void> addMood(
    String note,
    bool isSharedWithAI,
    double rating, {
    DateTime? selectedDate,
  }) async {
    if (rating == 1) {
      mood = 'Mad';
    } else if (rating == 2) {
      mood = 'Bad';
    } else if (rating == 3) {
      mood = 'OK';
    } else if (rating == 4) {
      mood = 'Happy';
    } else if (rating == 5) {
      mood = 'Joy';
    }

    debugPrint(note);
    debugPrint(isSharedWithAI.toString());
    debugPrint(rating.toString());
    debugPrint(mood);

    await di<IAddMoodLocalDb>().saveMood(
      MoodModel(
        rating: rating,
        createdAt: selectedDate ?? DateTime.now(),
        mood: mood,
        isSharedWithAI: isSharedWithAI,
        note: note,
      ),
    );

    await di<AnalyticsService>().logEvent(
      name: 'add_mood',
      parameters: {'mood': mood, 'rating': rating},
    );

    di<SnackBarShow>().showSnackBar(
      'Mood saved successfully!',
      AppTheme.blueberry,
    );

    if (isSharedWithAI) {
      // Get current user ID from auth
      final currentUserId = di<AuthController>().getCurrentUserId();

      debugPrint('Current user ID: $currentUserId');

      if (currentUserId != null) {
        try {
          await di<IBackendApi>().addMood(
            note,
            isSharedWithAI,
            rating,
            mood,
            currentUserId,
          );
          debugPrint('Mood successfully shared with AI backend');
        } catch (e) {
          debugPrint('Failed to share mood with AI backend: $e');
          di<SnackBarShow>().showSnackBar(
            'Mood saved locally, but failed to sync with AI. Please check your connection.',
            AppTheme.apricot,
          );
        }
      } else {
        debugPrint('Warning: User not logged in, cannot share mood with AI');
        di<SnackBarShow>().showSnackBar(
          'Please log in to share moods with AI',
          AppTheme.apricot,
        );
      }
    }

    // Refresh the moods list after adding
    await getMoods();
  }

  Future<void> deleteMood(int id) async {
    await di<IAddMoodLocalDb>().deleteMood(id);

    // Refresh the moods list after deleting
    await getMoods();
  }

  Future<void> sortMoodsByDate(DateTime date) async {
    _calenderMoods = await di<IAddMoodLocalDb>().getMoods();
    _calenderMoods =
        _calenderMoods.where((mood) {
          final moodDate = mood.createdAt ?? DateTime.now();
          return moodDate.year == date.year &&
              moodDate.month == date.month &&
              moodDate.day == date.day;
        }).toList();

    notifyListeners();
  }
}
