import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../repositories/i_notifications_prefs.dart';
import '../services/flutter_notifications_service.dart';
import '../theme/app_theme.dart';
import '../utilities/snack_bar_show.dart';

class NotificationsController extends ChangeNotifier {
  NotificationsController() {
    init();
  }

  bool _isloading = false;
  get isLoading => _isloading;

  bool _isNotificationsEnabled = false;
  bool get isNotificationsEnabled => _isNotificationsEnabled;

  String? _hours;
  get hours => _hours;

  late TimeOfDay _startTime;
  TimeOfDay get startTime => _startTime;

  late TimeOfDay _endTime;
  TimeOfDay get endTime => _endTime;

  Future<void> init() async {
    _isloading = true;
    notifyListeners();

    // Initialize flutter_local_notifications
    await di<FlutterNotificationsService>().init();

    final prefs = await di<INotificationsPrefs>().getNotificationsFromPrefs();
    _hours = prefs.hours;

    try {
      _startTime = _parseTimeString(prefs.startTime);
      _endTime = _parseTimeString(prefs.endTime);
    } catch (e) {
      _startTime = TimeOfDay(hour: 22, minute: 0); // 10:00 PM
      _endTime = TimeOfDay(hour: 7, minute: 0); // 7:00 AM
    }

    _isNotificationsEnabled = prefs.isNotificationsEnabled;
    _isloading = false;
    notifyListeners();
  }

  Future<void> toggleNotifications(bool value) async {
    _isNotificationsEnabled = value;
    notifyListeners();
  }

  void setHours(String? value) {
    _hours = value;
    notifyListeners();
  }

  void setStartTime(TimeOfDay time) {
    _startTime = time;
    notifyListeners();
  }

  void setEndTime(TimeOfDay time) {
    _endTime = time;
    notifyListeners();
  }

  String formatTimeOfDay(TimeOfDay time) {
    final hour = time.hourOfPeriod;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.period == DayPeriod.am ? 'AM' : 'PM';
    return '${hour == 0 ? 12 : hour}:$minute $period';
  }

  Future<void> saveInfoToPrefs() async {
    // Format time as 24-hour HH:mm
    final startTimeStr = _timeOfDayToString(_startTime);
    final endTimeStr = _timeOfDayToString(_endTime);

    await di<INotificationsPrefs>().saveNotificationsToPrefs(
      _hours ?? 'hourly', // Ensure we never save null
      startTimeStr,
      endTimeStr,
      _isNotificationsEnabled,
    );

    if (_isNotificationsEnabled) {
      // Schedule notifications
      await _scheduleFlutterLocalNotifications();
    } else {
      await di<FlutterNotificationsService>().cancelAllNotifications();
    }
  }

  // Schedule notifications using flutter_local_notifications (new system)
  Future<void> _scheduleFlutterLocalNotifications() async {
    final flutterNotificationsService = di<FlutterNotificationsService>();

    // Always request permissions first to ensure iOS permissions are properly handled
    // This won't show a dialog if permissions were already decided
    final bool permissionsGranted =
        await flutterNotificationsService.requestPermissions();

    if (!permissionsGranted) {
      debugPrint('Notification permissions not granted');
      return; // Exit if permissions not granted
    }

    // For Android 12+, check and request exact alarm permissions
    final bool canScheduleExact =
        await flutterNotificationsService.checkExactAlarmPermission();
    if (!canScheduleExact) {
      debugPrint('Exact alarm permission not granted, requesting...');
      final bool exactPermissionGranted =
          await flutterNotificationsService.requestExactAlarmPermission();
      if (!exactPermissionGranted) {
        debugPrint(
          'Exact alarm permission denied, will use inexact scheduling',
        );
        // Show user-friendly message
        di<SnackBarShow>().showSnackBar(
          'Notifications will be approximate due to system restrictions',
          AppTheme.apricot,
        );
      }
    }

    // Double-check if notifications are enabled (especially important for Android)
    final bool areNotificationsEnabled =
        await flutterNotificationsService.areNotificationsEnabled();

    if (!areNotificationsEnabled) {
      debugPrint('Notifications are not enabled');
      return; // Exit if notifications are not enabled
    }

    // Cancel existing notifications before scheduling new ones
    await flutterNotificationsService.cancelAllNotifications();

    // Get current time
    final now = DateTime.now();
    debugPrint('Scheduling notifications from: ${now.toString()}');

    // Convert start and end times to minutes since midnight for comparison
    final startTimeMinutes = _startTime.hour * 60 + _startTime.minute;
    final endTimeMinutes = _endTime.hour * 60 + _endTime.minute;

    // Check if end time is earlier than start time (crosses midnight)
    final crossesMidnight = endTimeMinutes < startTimeMinutes;
    debugPrint(
      'Quiet hours: ${_timeOfDayToString(_startTime)} to ${_timeOfDayToString(_endTime)}',
    );
    debugPrint('Crosses midnight: $crossesMidnight');

    // Track scheduled notifications for debugging
    int scheduledCount = 0;

    switch (_hours) {
      case 'hourly':
        // Schedule next 24 hourly notifications
        for (int i = 1; i <= 24; i++) {
          final scheduledTime = now.add(Duration(hours: i));
          if (_isTimeInAllowedRange(scheduledTime, crossesMidnight)) {
            await flutterNotificationsService.scheduleNotification(
              id: 1000 + i, // Use different ID range than easy_notifications
              title: 'Mood Reminder',
              body: 'Time to enter your mood!',
              scheduledDate: scheduledTime,
            );
            scheduledCount++;
          }
        }
        break;
      case '2hours':
        // Schedule next 12 two-hourly notifications
        for (int i = 1; i <= 12; i++) {
          final scheduledTime = now.add(Duration(hours: i * 2));
          if (_isTimeInAllowedRange(scheduledTime, crossesMidnight)) {
            await flutterNotificationsService.scheduleNotification(
              id: 2000 + i,
              title: 'Mood Reminder',
              body: 'Time to enter your mood!',
              scheduledDate: scheduledTime,
            );
            scheduledCount++;
          }
        }
        break;
      case '4hours':
        // Schedule next 6 four-hourly notifications
        for (int i = 1; i <= 6; i++) {
          final scheduledTime = now.add(Duration(hours: i * 4));
          if (_isTimeInAllowedRange(scheduledTime, crossesMidnight)) {
            await flutterNotificationsService.scheduleNotification(
              id: 4000 + i,
              title: 'Mood Reminder',
              body: 'Time to enter your mood!',
              scheduledDate: scheduledTime,
            );
            scheduledCount++;
          }
        }
        break;
      case 'daily':
        // Schedule daily notification at 10 AM
        var scheduledTime = DateTime(
          now.year,
          now.month,
          now.day,
          10, // 10 AM
          0,
        );

        // If 10 AM today has passed, schedule for tomorrow
        if (now.isAfter(scheduledTime)) {
          scheduledTime = scheduledTime.add(Duration(days: 1));
        }

        // Schedule next 7 days of notifications
        for (int i = 0; i < 7; i++) {
          final nextDayTime = scheduledTime.add(Duration(days: i));
          if (_isTimeInAllowedRange(nextDayTime, crossesMidnight)) {
            await flutterNotificationsService.scheduleNotification(
              id: 7000 + i,
              title: 'Daily Mood Reminder',
              body: 'Time to enter your mood!',
              scheduledDate: nextDayTime,
            );
            scheduledCount++;
          }
        }
        break;
    }

    debugPrint('Successfully scheduled $scheduledCount notifications');
  }

  // Helper method to check if a given time is within the allowed notification range
  // Note: _startTime and _endTime define the QUIET HOURS (when notifications should NOT be sent)
  bool _isTimeInAllowedRange(DateTime dateTime, bool crossesMidnight) {
    // Convert the time to minutes since midnight for easier comparison
    final timeMinutes = dateTime.hour * 60 + dateTime.minute;

    // Convert start and end times to minutes since midnight
    final startTimeMinutes = _startTime.hour * 60 + _startTime.minute;
    final endTimeMinutes = _endTime.hour * 60 + _endTime.minute;

    bool isAllowed;

    if (crossesMidnight) {
      // If quiet hours cross midnight (e.g., 10:00 PM to 7:00 AM)
      // Allow notifications if time is NOT between start and end
      // This means: DON'T send notifications if time is after start OR before end of next day
      isAllowed =
          !(timeMinutes >= startTimeMinutes || timeMinutes <= endTimeMinutes);
    } else {
      // Normal quiet hours range (e.g., 11:00 AM to 6:00 PM)
      // Allow notifications if time is NOT in the quiet hours
      isAllowed =
          !(timeMinutes >= startTimeMinutes && timeMinutes <= endTimeMinutes);
    }

    // Add debug logging
    debugPrint(
      'Time ${dateTime.hour}:${dateTime.minute} is ${isAllowed ? "allowed" : "not allowed"}',
    );
    return isAllowed;
  }

  // Helper method to convert TimeOfDay to string in 24-hour format
  String _timeOfDayToString(TimeOfDay time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  // Helper method to parse time string in 24-hour format to TimeOfDay
  TimeOfDay _parseTimeString(String timeStr) {
    final parts = timeStr.split(':');
    return TimeOfDay(hour: int.parse(parts[0]), minute: int.parse(parts[1]));
  }
}
