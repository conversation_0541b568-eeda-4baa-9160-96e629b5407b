import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/mood_controller.dart';
import '../theme/app_theme.dart';

class ChartMoodsWidget extends WatchingStatefulWidget {
  final double height;

  const ChartMoodsWidget({super.key, this.height = 240});

  @override
  State<ChartMoodsWidget> createState() => _ChartMoodsWidgetState();
}

class _ChartMoodsWidgetState extends State<ChartMoodsWidget> {
  int touchedIndex = -1;

  @override
  Widget build(BuildContext context) {
    final moodController = watchIt<MoodController>();
    final moodData = _calculateMoodPercentages(moodController.moods);

    if (moodData.isEmpty) {
      return _buildEmptyChart();
    }

    return Container(
      padding: const EdgeInsets.only(top: 8, bottom: 8, left: 16, right: 16),
      child: Column(
        children: [
          Text(
            'Mood Distribution',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.blueberry,
            ),
          ),
          // Pie chart
          Row(
            children: [
              // Pie chart
              Expanded(
                flex: 3,
                child: SizedBox(
                  height: widget.height,
                  child: PieChart(
                    PieChartData(
                      pieTouchData: PieTouchData(
                        touchCallback: (FlTouchEvent event, pieTouchResponse) {
                          setState(() {
                            if (!event.isInterestedForInteractions ||
                                pieTouchResponse == null ||
                                pieTouchResponse.touchedSection == null) {
                              touchedIndex = -1;
                              return;
                            }
                            touchedIndex =
                                pieTouchResponse
                                    .touchedSection!
                                    .touchedSectionIndex;
                          });
                        },
                      ),
                      borderData: FlBorderData(show: false),
                      sectionsSpace: 2,
                      centerSpaceRadius:
                          widget.height * 0.13, // Scale with height
                      sections: _buildPieChartSections(moodData),
                    ),
                  ),
                ),
              ),

              // Spacing between chart and legend
              const SizedBox(width: 20),

              // Legend
              Expanded(flex: 2, child: _buildLegend(moodData)),
            ],
          ),
        ],
      ),
    );
  }

  Map<String, double> _calculateMoodPercentages(List moods) {
    if (moods.isEmpty) return {};

    Map<String, int> moodCounts = {};

    for (var mood in moods) {
      String moodName = mood.mood ?? 'Unknown';
      moodCounts[moodName] = (moodCounts[moodName] ?? 0) + 1;
    }

    Map<String, double> percentages = {};
    int totalMoods = moods.length;

    moodCounts.forEach((mood, count) {
      percentages[mood] = (count / totalMoods) * 100;
    });

    return percentages;
  }

  List<PieChartSectionData> _buildPieChartSections(
    Map<String, double> moodData,
  ) {
    List<PieChartSectionData> sections = [];
    int index = 0;

    moodData.forEach((mood, percentage) {
      final isTouched = index == touchedIndex;
      final baseRadius = widget.height * 0.33; // Scale with height
      final radius = isTouched ? baseRadius + 10 : baseRadius;
      final fontSize = isTouched ? 16.0 : 14.0;

      sections.add(
        PieChartSectionData(
          color: _getMoodColor(mood),
          value: percentage,
          title: '${percentage.toStringAsFixed(1)}%',
          radius: radius,
          titleStyle: TextStyle(
            fontSize: fontSize,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            shadows: [
              Shadow(
                color: Colors.black.withValues(alpha: 0.5),
                offset: const Offset(1, 1),
                blurRadius: 2,
              ),
            ],
          ),
        ),
      );
      index++;
    });

    return sections;
  }

  Widget _buildLegend(Map<String, double> moodData) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children:
              moodData.entries.map((entry) {
                return Container(
                  margin: const EdgeInsets.symmetric(vertical: 3),
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: _getMoodColor(entry.key).withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 14,
                        height: 14,
                        decoration: BoxDecoration(
                          color: _getMoodColor(entry.key),
                          borderRadius: BorderRadius.circular(7),
                          boxShadow: [
                            BoxShadow(
                              color: _getMoodColor(
                                entry.key,
                              ).withValues(alpha: 0.3),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              entry.key,
                              style: Theme.of(
                                context,
                              ).textTheme.bodySmall?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: _getMoodColor(entry.key),
                              ),
                            ),
                            Text(
                              '${entry.value.toStringAsFixed(1)}%',
                              style: Theme.of(
                                context,
                              ).textTheme.bodySmall?.copyWith(
                                color: Theme.of(
                                  context,
                                ).colorScheme.onSurface.withValues(alpha: 0.7),
                                fontWeight: FontWeight.w500,
                                fontSize: 10,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
        ),
      ),
    );
  }

  Widget _buildEmptyChart() {
    return Container(
      height: widget.height,
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.pie_chart_outline,
            size: widget.height * 0.27, // Scale with height
            color: AppTheme.blueberry.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No Mood Data Yet',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppTheme.blueberry,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start tracking your moods to see your distribution chart',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getMoodColor(String mood) {
    switch (mood.toLowerCase()) {
      case 'mad':
        return const Color(0xFFFF0000);
      case 'bad':
        return const Color(0xFFFF5600);
      case 'ok':
        return const Color(0xFF0070FF);
      case 'happy':
        return const Color(0xFFFFC800);
      case 'joy':
        return const Color(0xFF25FF2E);
      default:
        return const Color(0xFF38B2AC);
    }
  }
}
