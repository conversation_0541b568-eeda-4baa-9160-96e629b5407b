import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../repositories/i_auth_repository.dart';
import '../router/rounter.dart';
import '../utilities/snack_bar_show.dart';

class AuthController {
  final IAuthRepository _authRepository;

  AuthController(this._authRepository);

  Future<void> register(String email, String password) async {
    try {
      final user = await _authRepository.register(email, password);
      if (user != null) {
        debugPrint(user.uid.toString());

        router.go('/login');
      }
    } on FirebaseAuthException catch (e) {
      debugPrint(e.toString());
      debugPrint(e.code.toString());

      String message = '';

      switch (e.code) {
        case 'weak-password':
          debugPrint('The password provided is too weak.');
          message = 'The password provided is too weak.';
          break;
        case 'email-already-in-use':
          message = 'The account already exists for that email.';
          debugPrint('The account already exists for that email.');
          break;
        case 'unknown':
          if (e.message?.contains('CONFIGURATION_NOT_FOUND') == true) {
            message = 'Firebase configuration error. Please check your setup.';
            debugPrint(
              'Firebase configuration not found. Check GoogleService-Info.plist and google-services.json files.',
            );
          } else {
            message = 'An unknown error occurred. Please try again.';
          }
          break;
        case 'invalid-email':
          message = 'The email address is not valid.';
          break;
        case 'operation-not-allowed':
          message = 'Email/password accounts are not enabled.';
          break;
        default:
          message = 'Registration failed: ${e.message}';
      }

      di<SnackBarShow>().showSnackBar(message, Colors.red);
    } catch (e) {
      debugPrint('Unexpected error: $e');
      di<SnackBarShow>().showSnackBar(
        'An unexpected error occurred. Please try again.',
        Colors.red,
      );
    }
  }

  Future<void> login(String email, String password) async {
    try {
      final user = await _authRepository.login(email, password);

      if (user != null) {
        debugPrint(user.uid.toString());
        router.go('/mainScreen');
      }
    } on FirebaseAuthException catch (e) {
      debugPrint(e.toString());
      debugPrint(e.code.toString());

      String message = '';

      switch (e.code) {
        case 'user-not-found':
          message = 'No user found for that email.';
          break;
        case 'wrong-password':
          message = 'Wrong password provided.';
          break;
        case 'invalid-email':
          message = 'The email address is not valid.';
          break;
        case 'user-disabled':
          message = 'This user account has been disabled.';
          break;
        case 'too-many-requests':
          message = 'Too many failed login attempts. Please try again later.';
          break;
        case 'unknown':
          if (e.message?.contains('CONFIGURATION_NOT_FOUND') == true) {
            message = 'Firebase configuration error. Please check your setup.';
            debugPrint(
              'Firebase configuration not found. Check GoogleService-Info.plist and google-services.json files.',
            );
          } else {
            message = 'An unknown error occurred. Please try again.';
          }
          break;
        default:
          message = 'Login failed: ${e.message}';
      }

      di<SnackBarShow>().showSnackBar(message, Colors.red);
    } catch (e) {
      debugPrint('Unexpected error: $e');
      di<SnackBarShow>().showSnackBar(
        'An unexpected error occurred. Please try again.',
        Colors.red,
      );
    }
  }

  Future<void> logout() async {
    try {
      await _authRepository.logout();

      router.go('/');
    } catch (e) {
      throw Exception('Logout failed: $e');
    }
  }

  /// Get the current logged-in user
  User? getCurrentUser() {
    return _authRepository.getCurrentUser();
  }

  /// Get the current logged-in user's ID
  String? getCurrentUserId() {
    return _authRepository.getCurrentUserId();
  }

  /// Check if user is logged in
  bool isLoggedIn() {
    return getCurrentUser() != null;
  }
}
