import 'package:dash_chat_2/dash_chat_2.dart';
import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../repositories/i_backend_api.dart';
import '../utilities/markdown_remover.dart';
import 'auth_controller.dart';

class ChatController extends ChangeNotifier {
  ChatController() {
    init();
  }

  ChatUser user = ChatUser(id: '1');
  ChatUser ai = ChatUser(id: '2', firstName: 'A', lastName: 'I');

  List<ChatMessage> messages = <ChatMessage>[];

  bool _isLoading = false;

  get isLoading => _isLoading;

  Future<void> init() async {
    // Initialize any necessary data or perform any setup tasks
  }

  Future<void> sendMessage(String message) async {
    // Implement the logic to send the message to the AI chatbot

    _isLoading = true;
    notifyListeners();

    messages.insert(
      0,
      ChatMessage(createdAt: DateTime.now(), text: message, user: user),
    );

    await Future.delayed(const Duration(seconds: 2));

    // Get current user ID from auth
    final currentUserId = di<AuthController>().getCurrentUserId();

    if (currentUserId != null) {
      try {
        final response = await di<IBackendApi>().sendToAIMessage(
          message,
          currentUserId,
        );

        // Remove markdown formatting from the AI response
        if (response.statusCode == 200) {
          final aiResponse = response.data['answer'] ?? '';
          final cleanResponse = MarkdownRemover.removeMarkdown(aiResponse);

          messages.insert(
            0,
            ChatMessage(
              createdAt: DateTime.now(),
              text: cleanResponse,
              user: ai,
            ),
          );
        } else {
          debugPrint('Failed to send message to AI: ${response.statusCode}');
        }
      } catch (e) {
        debugPrint('Failed to send message to AI: $e');
      }
    }

    _isLoading = false;
    notifyListeners();
  }
}
