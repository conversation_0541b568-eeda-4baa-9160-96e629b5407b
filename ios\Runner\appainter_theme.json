{"applyElevationOverlayColor": false, "brightness": "light", "buttonTheme": {"alignedDropdown": false, "colorScheme": {"brightness": "light", "error": "#ffb00020", "errorContainer": "#ffb00020", "inversePrimary": "#ffffffff", "inverseSurface": "#ff000000", "onError": "#ffffffff", "onErrorContainer": "#ffffffff", "onInverseSurface": "#fff4f2f7", "onPrimaryFixed": "#ffffffff", "onPrimary": "#ffffffff", "onPrimaryContainer": "#ffffffff", "onPrimaryFixedVariant": "#ffffffff", "onSecondary": "#ff000000", "onSecondaryContainer": "#ff000000", "onSecondaryFixed": "#ff000000", "onSecondaryFixedVariant": "#ff000000", "onSurface": "#ff000000", "onSurfaceVariant": "#ff000000", "onTertiary": "#ff000000", "onTertiaryContainer": "#ff000000", "onTertiaryFixed": "#ff000000", "onTertiaryFixedVariant": "#ff000000", "outline": "#ff000000", "outlineVariant": "#ff000000", "primary": "#ff513f18", "primaryContainer": "#ff372a0e", "primaryFixed": "#ff513f18", "primaryFixedDim": "#ff513f18", "scrim": "#ff000000", "secondary": "#ff194a36", "secondaryContainer": "#ff0e3223", "surfaceBright": "#fff4f2f7", "surfaceContainer": "#fff4f2f7", "surfaceContainerHigh": "#fff4f2f7", "surfaceContainerHighest": "#fff4f2f7", "secondaryFixed": "#ff194a36", "secondaryFixedDim": "#ff194a36", "shadow": "#ff000000", "surface": "#fff4f2f7", "surfaceContainerLow": "#fff4f2f7", "surfaceContainerLowest": "#fff4f2f7", "surfaceDim": "#fff4f2f7", "tertiary": "#ff194a36", "tertiaryContainer": "#ff194a36", "tertiaryFixed": "#ff194a36", "tertiaryFixedDim": "#ff194a36"}, "height": 36, "layoutBehavior": "padded", "minWidth": 88, "padding": {"bottom": 0, "left": 16, "right": 16, "top": 0}, "shape": {"borderRadius": {"bottomLeft": {"type": "elliptical", "x": 2, "y": 2}, "bottomRight": {"type": "elliptical", "x": 2, "y": 2}, "topLeft": {"type": "elliptical", "x": 2, "y": 2}, "topRight": {"type": "elliptical", "x": 2, "y": 2}, "type": "only"}, "side": {"color": "#ff000000", "strokeAlign": -1, "style": "none", "width": 0}, "type": "rounded"}, "textTheme": "normal"}, "canvasColor": "#fff4f2f7", "cardColor": "#fff4f2f7", "colorScheme": {"brightness": "light", "error": "#ffb00020", "errorContainer": "#ffb00020", "inversePrimary": "#ffffffff", "inverseSurface": "#ff000000", "onError": "#ffffffff", "onErrorContainer": "#ffffffff", "onInverseSurface": "#fff4f2f7", "onPrimaryFixed": "#ffffffff", "onPrimary": "#ffffffff", "onPrimaryContainer": "#ffffffff", "onPrimaryFixedVariant": "#ffffffff", "onSecondary": "#ff000000", "onSecondaryContainer": "#ff000000", "onSecondaryFixed": "#ff000000", "onSecondaryFixedVariant": "#ff000000", "onSurface": "#ff000000", "onSurfaceVariant": "#ff000000", "onTertiary": "#ff000000", "onTertiaryContainer": "#ff000000", "onTertiaryFixed": "#ff000000", "onTertiaryFixedVariant": "#ff000000", "outline": "#ff000000", "outlineVariant": "#ff000000", "primary": "#ff513f18", "primaryContainer": "#ff372a0e", "primaryFixed": "#ff513f18", "primaryFixedDim": "#ff513f18", "scrim": "#ff000000", "secondary": "#ff194a36", "secondaryContainer": "#ff0e3223", "surfaceBright": "#fff4f2f7", "surfaceContainer": "#fff4f2f7", "surfaceContainerHigh": "#fff4f2f7", "surfaceContainerHighest": "#fff4f2f7", "secondaryFixed": "#ff194a36", "secondaryFixedDim": "#ff194a36", "shadow": "#ff000000", "surface": "#fff4f2f7", "surfaceContainerLow": "#fff4f2f7", "surfaceContainerLowest": "#fff4f2f7", "surfaceDim": "#fff4f2f7", "tertiary": "#ff194a36", "tertiaryContainer": "#ff194a36", "tertiaryFixed": "#ff194a36", "tertiaryFixedDim": "#ff194a36"}, "disabledColor": "#61000000", "dividerColor": "#1f000000", "focusColor": "#1f000000", "highlightColor": "#66bcbcbc", "hintColor": "#99000000", "hoverColor": "#0a000000", "iconTheme": {"color": "#dd000000"}, "indicatorColor": "#ffffffff", "inputDecorationTheme": {"alignLabelWithHint": false, "filled": false, "floatingLabelAlignment": "start", "floatingLabelBehavior": "auto", "isCollapsed": false, "isDense": false}, "materialTapTargetSize": "shrinkWrap", "platform": "windows", "primaryColor": "#ff513f18", "primaryColorDark": "#ff1976d2", "primaryColorLight": "#ffbbdefb", "primaryIconTheme": {"color": "#ffffffff"}, "primaryTextTheme": {"bodyLarge": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "fontSize": 16, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.5, "textBaseline": "alphabetic"}, "bodyMedium": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "fontSize": 14, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.25, "textBaseline": "alphabetic"}, "bodySmall": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "fontSize": 12, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.4, "textBaseline": "alphabetic"}, "displayLarge": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "fontSize": 96, "fontWeight": "w300", "inherit": false, "letterSpacing": -1.5, "textBaseline": "alphabetic"}, "displayMedium": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "fontSize": 60, "fontWeight": "w300", "inherit": false, "letterSpacing": -0.5, "textBaseline": "alphabetic"}, "displaySmall": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "fontSize": 48, "fontWeight": "w400", "inherit": false, "letterSpacing": 0, "textBaseline": "alphabetic"}, "headlineLarge": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "fontSize": 40, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.25, "textBaseline": "alphabetic"}, "headlineMedium": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "fontSize": 34, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.25, "textBaseline": "alphabetic"}, "headlineSmall": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "fontSize": 24, "fontWeight": "w400", "inherit": false, "letterSpacing": 0, "textBaseline": "alphabetic"}, "labelLarge": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "fontSize": 14, "fontWeight": "w500", "inherit": false, "letterSpacing": 1.25, "textBaseline": "alphabetic"}, "labelMedium": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "fontSize": 11, "fontWeight": "w400", "inherit": false, "letterSpacing": 1.5, "textBaseline": "alphabetic"}, "labelSmall": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "fontSize": 10, "fontWeight": "w400", "inherit": false, "letterSpacing": 1.5, "textBaseline": "alphabetic"}, "titleLarge": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "fontSize": 20, "fontWeight": "w500", "inherit": false, "letterSpacing": 0.15, "textBaseline": "alphabetic"}, "titleMedium": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "fontSize": 16, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.15, "textBaseline": "alphabetic"}, "titleSmall": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "fontSize": 14, "fontWeight": "w500", "inherit": false, "letterSpacing": 0.1, "textBaseline": "alphabetic"}}, "scaffoldBackgroundColor": "#fff4f2f7", "secondaryHeaderColor": "#ffe3f2fd", "shadowColor": "#ff000000", "splashColor": "#66c8c8c8", "splashFactory": "ripple", "textTheme": {"bodyLarge": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "fontSize": 16, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.5, "textBaseline": "alphabetic"}, "bodyMedium": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "fontSize": 14, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.25, "textBaseline": "alphabetic"}, "bodySmall": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "fontSize": 12, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.4, "textBaseline": "alphabetic"}, "displayLarge": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "fontSize": 96, "fontWeight": "w300", "inherit": false, "letterSpacing": -1.5, "textBaseline": "alphabetic"}, "displayMedium": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "fontSize": 60, "fontWeight": "w300", "inherit": false, "letterSpacing": -0.5, "textBaseline": "alphabetic"}, "displaySmall": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "fontSize": 48, "fontWeight": "w400", "inherit": false, "letterSpacing": 0, "textBaseline": "alphabetic"}, "headlineLarge": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "fontSize": 40, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.25, "textBaseline": "alphabetic"}, "headlineMedium": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "fontSize": 34, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.25, "textBaseline": "alphabetic"}, "headlineSmall": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "fontSize": 24, "fontWeight": "w400", "inherit": false, "letterSpacing": 0, "textBaseline": "alphabetic"}, "labelLarge": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "fontSize": 14, "fontWeight": "w500", "inherit": false, "letterSpacing": 1.25, "textBaseline": "alphabetic"}, "labelMedium": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "fontSize": 11, "fontWeight": "w400", "inherit": false, "letterSpacing": 1.5, "textBaseline": "alphabetic"}, "labelSmall": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "fontSize": 10, "fontWeight": "w400", "inherit": false, "letterSpacing": 1.5, "textBaseline": "alphabetic"}, "titleLarge": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "fontSize": 20, "fontWeight": "w500", "inherit": false, "letterSpacing": 0.15, "textBaseline": "alphabetic"}, "titleMedium": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "fontSize": 16, "fontWeight": "w400", "inherit": false, "letterSpacing": 0.15, "textBaseline": "alphabetic"}, "titleSmall": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "fontSize": 14, "fontWeight": "w500", "inherit": false, "letterSpacing": 0.1, "textBaseline": "alphabetic"}}, "typography": {"black": {"bodyLarge": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "inherit": true}, "bodyMedium": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "inherit": true}, "bodySmall": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "inherit": true}, "displayLarge": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "inherit": true}, "displayMedium": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "inherit": true}, "displaySmall": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "inherit": true}, "headlineLarge": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "inherit": true}, "headlineMedium": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "inherit": true}, "headlineSmall": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "inherit": true}, "labelLarge": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "inherit": true}, "labelMedium": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "inherit": true}, "labelSmall": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "inherit": true}, "titleLarge": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "inherit": true}, "titleMedium": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "inherit": true}, "titleSmall": {"color": "#ff000000", "decoration": "none", "decorationColor": "#ff000000", "fontFamily": "Segoe UI", "inherit": true}}, "dense": {"bodyLarge": {"fontSize": 16, "fontWeight": "w400", "height": 1.5, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.5, "textBaseline": "ideographic"}, "bodyMedium": {"fontSize": 14, "fontWeight": "w400", "height": 1.43, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.25, "textBaseline": "ideographic"}, "bodySmall": {"fontSize": 12, "fontWeight": "w400", "height": 1.33, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.4, "textBaseline": "ideographic"}, "displayLarge": {"fontSize": 57, "fontWeight": "w400", "height": 1.12, "inherit": false, "leadingDistribution": "even", "letterSpacing": -0.25, "textBaseline": "ideographic"}, "displayMedium": {"fontSize": 45, "fontWeight": "w400", "height": 1.16, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "ideographic"}, "displaySmall": {"fontSize": 36, "fontWeight": "w400", "height": 1.22, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "ideographic"}, "headlineLarge": {"fontSize": 32, "fontWeight": "w400", "height": 1.25, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "ideographic"}, "headlineMedium": {"fontSize": 28, "fontWeight": "w400", "height": 1.29, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "ideographic"}, "headlineSmall": {"fontSize": 24, "fontWeight": "w400", "height": 1.33, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "ideographic"}, "labelLarge": {"fontSize": 14, "fontWeight": "w500", "height": 1.43, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.1, "textBaseline": "ideographic"}, "labelMedium": {"fontSize": 12, "fontWeight": "w500", "height": 1.33, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.5, "textBaseline": "ideographic"}, "labelSmall": {"fontSize": 11, "fontWeight": "w500", "height": 1.45, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.5, "textBaseline": "ideographic"}, "titleLarge": {"fontSize": 22, "fontWeight": "w400", "height": 1.27, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "ideographic"}, "titleMedium": {"fontSize": 16, "fontWeight": "w500", "height": 1.5, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.15, "textBaseline": "ideographic"}, "titleSmall": {"fontSize": 14, "fontWeight": "w500", "height": 1.43, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.1, "textBaseline": "ideographic"}}, "englishLike": {"bodyLarge": {"fontSize": 16, "fontWeight": "w400", "height": 1.5, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.5, "textBaseline": "alphabetic"}, "bodyMedium": {"fontSize": 14, "fontWeight": "w400", "height": 1.43, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.25, "textBaseline": "alphabetic"}, "bodySmall": {"fontSize": 12, "fontWeight": "w400", "height": 1.33, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.4, "textBaseline": "alphabetic"}, "displayLarge": {"fontSize": 57, "fontWeight": "w400", "height": 1.12, "inherit": false, "leadingDistribution": "even", "letterSpacing": -0.25, "textBaseline": "alphabetic"}, "displayMedium": {"fontSize": 45, "fontWeight": "w400", "height": 1.16, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "displaySmall": {"fontSize": 36, "fontWeight": "w400", "height": 1.22, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "headlineLarge": {"fontSize": 32, "fontWeight": "w400", "height": 1.25, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "headlineMedium": {"fontSize": 28, "fontWeight": "w400", "height": 1.29, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "headlineSmall": {"fontSize": 24, "fontWeight": "w400", "height": 1.33, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "labelLarge": {"fontSize": 14, "fontWeight": "w500", "height": 1.43, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.1, "textBaseline": "alphabetic"}, "labelMedium": {"fontSize": 12, "fontWeight": "w500", "height": 1.33, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.5, "textBaseline": "alphabetic"}, "labelSmall": {"fontSize": 11, "fontWeight": "w500", "height": 1.45, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.5, "textBaseline": "alphabetic"}, "titleLarge": {"fontSize": 22, "fontWeight": "w400", "height": 1.27, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "titleMedium": {"fontSize": 16, "fontWeight": "w500", "height": 1.5, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.15, "textBaseline": "alphabetic"}, "titleSmall": {"fontSize": 14, "fontWeight": "w500", "height": 1.43, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.1, "textBaseline": "alphabetic"}}, "tall": {"bodyLarge": {"fontSize": 16, "fontWeight": "w400", "height": 1.5, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.5, "textBaseline": "alphabetic"}, "bodyMedium": {"fontSize": 14, "fontWeight": "w400", "height": 1.43, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.25, "textBaseline": "alphabetic"}, "bodySmall": {"fontSize": 12, "fontWeight": "w400", "height": 1.33, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.4, "textBaseline": "alphabetic"}, "displayLarge": {"fontSize": 57, "fontWeight": "w400", "height": 1.12, "inherit": false, "leadingDistribution": "even", "letterSpacing": -0.25, "textBaseline": "alphabetic"}, "displayMedium": {"fontSize": 45, "fontWeight": "w400", "height": 1.16, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "displaySmall": {"fontSize": 36, "fontWeight": "w400", "height": 1.22, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "headlineLarge": {"fontSize": 32, "fontWeight": "w400", "height": 1.25, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "headlineMedium": {"fontSize": 28, "fontWeight": "w400", "height": 1.29, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "headlineSmall": {"fontSize": 24, "fontWeight": "w400", "height": 1.33, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "labelLarge": {"fontSize": 14, "fontWeight": "w500", "height": 1.43, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.1, "textBaseline": "alphabetic"}, "labelMedium": {"fontSize": 12, "fontWeight": "w500", "height": 1.33, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.5, "textBaseline": "alphabetic"}, "labelSmall": {"fontSize": 11, "fontWeight": "w500", "height": 1.45, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.5, "textBaseline": "alphabetic"}, "titleLarge": {"fontSize": 22, "fontWeight": "w400", "height": 1.27, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0, "textBaseline": "alphabetic"}, "titleMedium": {"fontSize": 16, "fontWeight": "w500", "height": 1.5, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.15, "textBaseline": "alphabetic"}, "titleSmall": {"fontSize": 14, "fontWeight": "w500", "height": 1.43, "inherit": false, "leadingDistribution": "even", "letterSpacing": 0.1, "textBaseline": "alphabetic"}}, "white": {"bodyLarge": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "inherit": true}, "bodyMedium": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "inherit": true}, "bodySmall": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "inherit": true}, "displayLarge": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "inherit": true}, "displayMedium": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "inherit": true}, "displaySmall": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "inherit": true}, "headlineLarge": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "inherit": true}, "headlineMedium": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "inherit": true}, "headlineSmall": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "inherit": true}, "labelLarge": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "inherit": true}, "labelMedium": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "inherit": true}, "labelSmall": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "inherit": true}, "titleLarge": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "inherit": true}, "titleMedium": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "inherit": true}, "titleSmall": {"color": "#fff4f2f7", "decoration": "none", "decorationColor": "#fff4f2f7", "fontFamily": "Segoe UI", "inherit": true}}}, "unselectedWidgetColor": "#8a000000", "useMaterial3": true, "visualDensity": "compact"}