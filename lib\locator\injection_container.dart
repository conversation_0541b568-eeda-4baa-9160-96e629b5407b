import 'package:firebase_auth/firebase_auth.dart';
import 'package:objectbox/objectbox.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/auth_controller.dart';
import '../controllers/chat_controller.dart';
import '../controllers/journal_contoller.dart';
import '../controllers/mood_controller.dart';
import '../controllers/theme_mode_controller.dart';
import '../models/jounal.dart';
import '../models/mood.dart';
import '../repositories/add_jounal_local_db.dart';
import '../repositories/add_mood_local_db.dart';
import '../repositories/auth_repository.dart';
import '../repositories/backend_api.dart';
import '../repositories/dark_mode_local_db.dart';
import '../repositories/i_add_jounal_local_db.dart';
import '../repositories/i_add_mood_local_db.dart';
import '../repositories/i_auth_repository.dart';
import '../repositories/i_backend_api.dart';
import '../repositories/i_dark_mode_local_db.dart';
import '../services/analytics_service.dart';
import '../utilities/snack_bar_show.dart';

initializeDependencies(Store store) {
  di.registerSingleton<Store>(store);

  di.registerSingleton<Box<MoodModel>>(store.box<MoodModel>());

  di.registerLazySingleton<IAddMoodLocalDb>(
    () => AddMoodLocalDb(di<Box<MoodModel>>()),
  );

  di.registerSingleton<Box<JournalModel>>(store.box<JournalModel>());

  di.registerLazySingleton<IAddJournalLocalDb>(
    () => AddJournalLocalDb(di<Box<JournalModel>>()),
  );

  di.registerLazySingleton<FirebaseAuth>(() => FirebaseAuth.instance);

  di.registerLazySingleton<IAuthRepository>(
    () => AuthRepository(di<FirebaseAuth>()),
  );

  // Auth Controller
  di.registerFactory<AuthController>(
    () => AuthController(di<IAuthRepository>()),
  );

  di.registerSingleton<SnackBarShow>(SnackBarShow());

  di.registerSingleton<MoodController>(MoodController());

  di.registerLazySingleton<IDarkModeLocalDb>(() => DarkModeLocalDb());

  di.registerSingleton<ThemeModeController>(ThemeModeController());

  di.registerLazySingleton<JournalController>(() => JournalController());

  di.registerLazySingleton<ChatController>(() => ChatController());

  di.registerLazySingleton<AnalyticsService>(() => AnalyticsService());

  di.registerLazySingleton<IBackendApi>(() => BackendApi());
}
