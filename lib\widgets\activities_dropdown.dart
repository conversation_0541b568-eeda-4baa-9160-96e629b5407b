class ActivitiesDropdown extends StatefulWidget {
  final Function(String)? onActivitySelected;

  const ActivitiesDropdown({super.key, this.onActivitySelected});

  @override
  State<ActivitiesDropdown> createState() => _ActivitiesDropdownState();
}

class _ActivitiesDropdownState extends State<ActivitiesDropdown> {
  String? selectedActivity;

  @override
  Widget build(BuildContext context) {
    final moodController = watchIt<MoodController>();

    return DropdownButtonFormField<String>(
      value: selectedActivity,
      items:
          moodController.activities.map((activity) {
            return DropdownMenuItem<String>(
              value: activity.activityName,
              child: Text(activity.activityName),
            );
          }).toList(),
      onChanged: (value) {
        setState(() {
          selectedActivity = value;
        });
        if (widget.onActivitySelected != null) {
          widget.onActivitySelected!(value!);
        }
      },
      decoration: InputDecoration(
        labelText: 'Activity',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }
}
