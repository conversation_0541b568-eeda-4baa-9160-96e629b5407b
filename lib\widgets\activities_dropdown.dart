import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/mood_controller.dart';
import '../theme/app_theme.dart';

class ActivitiesDropdown extends WatchingStatefulWidget {
  final String? selectedActivity;
  final ValueChanged<String?>? onChanged;
  final String? hintText;
  final bool enabled;

  const ActivitiesDropdown({
    super.key,
    this.selectedActivity,
    this.onChanged,
    this.hintText,
    this.enabled = true,
  });

  @override
  State<ActivitiesDropdown> createState() => _ActivitiesDropdownState();
}

class _ActivitiesDropdownState extends State<ActivitiesDropdown> {
  @override
  Widget build(BuildContext context) {
    final activities = watchIt<MoodController>().activities;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.citrus.withValues(alpha: 0.1),
            AppTheme.appleCore.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.blueberry.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: widget.selectedActivity,
          hint: Text(
            widget.hintText ?? 'Select an activity',
            style: TextStyle(
              color: AppTheme.blueberry.withValues(alpha: 0.6),
              fontSize: 18,
            ),
          ),
          icon: Icon(
            Icons.keyboard_arrow_down,
            color: AppTheme.blueberry.withValues(alpha: 0.7),
          ),
          isExpanded: true,
          style: TextStyle(
            color: AppTheme.blueberry,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
          dropdownColor: Colors.white,
          borderRadius: BorderRadius.circular(12),
          elevation: 8,
          onChanged: widget.enabled ? widget.onChanged : null,
          items:
              activities.map<DropdownMenuItem<String>>((activity) {
                return DropdownMenuItem<String>(
                  value: activity.activityName,
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Row(
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: AppTheme.citrus,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            activity.activityName,
                            style: TextStyle(
                              color: AppTheme.blueberry,
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
        ),
      ),
    );
  }
}
