import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:watch_it/watch_it.dart';

import '../models/jounal.dart' show JournalModel;
import '../repositories/i_add_jounal_local_db.dart';
import '../repositories/i_backend_api.dart';
import '../services/analytics_service.dart';
import '../theme/app_theme.dart';
import '../utilities/snack_bar_show.dart';
import 'auth_controller.dart';

class JournalController extends ChangeNotifier {
  JournalController() {
    init();
  }

  Future<void> init() async {
    await getJournals();
  }

  List<JournalModel> journals = [];

  bool get isLoading => _isLoading;
  bool _isLoading = false;

  Future<void> addJournalEntry(
    String note,
    bool isSharedWithAI,
    XFile? image,
    String? imagePathPermanent,
  ) async {
    debugPrint(note);
    debugPrint(isSharedWithAI.toString());
    debugPrint(image?.path);
    debugPrint(imagePathPermanent);

    await di<IAddJournalLocalDb>().saveJournal(
      JournalModel(
        note: note,
        createdAt: DateTime.now(),
        isSharedWithAI: isSharedWithAI,
        imagePath: imagePathPermanent,
      ),
    );

    await di<AnalyticsService>().logEvent(
      name: 'add_journal_entry',
      parameters: {
        'shared_with_ai': isSharedWithAI,
        'note_length': note.length,
      },
    );

    di<SnackBarShow>().showSnackBar(
      'Journal entry saved successfully!',
      AppTheme.blueberry,
    );

    if (isSharedWithAI) {
      // Get current user ID from auth
      final currentUserId = di<AuthController>().getCurrentUserId();

      debugPrint('Current user ID: $currentUserId');

      if (currentUserId != null) {
        try {
          await di<IBackendApi>().addJournalEntry(
            note,
            isSharedWithAI,
            currentUserId,
          );
          debugPrint('Journal entry successfully shared with AI backend');
        } catch (e) {
          debugPrint('Failed to share journal entry with AI backend: $e');
          di<SnackBarShow>().showSnackBar(
            'Journal entry saved locally, but failed to sync with AI. Please check your connection.',
            AppTheme.apricot,
          );
        }
      } else {
        debugPrint('Warning: User not logged in, cannot share journal entry with AI');
        di<SnackBarShow>().showSnackBar(
          'Please log in to share journal entries with AI',
          AppTheme.apricot,
        );
      }
    }




    await getJournals();
  }

  Future<void> getJournals() async {
    _isLoading = true;
    notifyListeners();
    journals = await di<IAddJournalLocalDb>().getJournals();
    _isLoading = false;
    notifyListeners();
  }

  Future<void> deleteJournalEntry(int id) async {
    await di<IAddJournalLocalDb>().deleteJournal(id);

    // Refresh the journal list after deleting
    await getJournals();
  }
}
