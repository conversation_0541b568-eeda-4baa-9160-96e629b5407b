import 'package:dash_chat_2/dash_chat_2.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:watch_it/watch_it.dart';

import '../../controllers/chat_controller.dart';
import '../../theme/app_theme.dart';
import '../../theme/icon_size.dart';

class ChatScreen extends WatchingStatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final controller = TextEditingController();

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final ai = watchIt<ChatController>();

    return Scaffold(
      backgroundColor:
          Theme.of(context).brightness == Brightness.light
              ? AppTheme.lightBackground
              : AppTheme.darkBackground,
      appBar: AppBar(
        title: const Text('Chat with MoodVibe'),
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {
              context.push('/settings');
            },
            icon: const Icon(Icons.settings, size: AppIconSize.appIconSize),
          ),
        ],
      ),
      body:
          ai.isLoading
              ? Center(
                child: SizedBox(
                  width: MediaQuery.of(context).size.width / 2,
                  child: LoadingIndicator(
                    indicatorType: Indicator.ballTrianglePathColoredFilled,

                    /// Required, The loading type of the widget
                    colors: const [
                      AppTheme.apricot,
                      AppTheme.blueberry,
                      AppTheme.citrus,
                    ],

                    /// Optional, The color collections
                    strokeWidth: 2,

                    /// Optional, The stroke of the line, only applicable to widget which contains line
                    backgroundColor: Colors.transparent,

                    /// Optional, the stroke backgroundColor
                  ),
                ),
              )
              : Column(
                children: [
                  Expanded(
                    child: DashChat(
                      readOnly: true,
                      currentUser: ai.user,
                      messages: ai.messages,
                      onSend: (ChatMessage m) {
                        setState(() {
                          ai.messages.insert(0, m);
                        });
                      },
                      messageOptions: MessageOptions(
                        borderRadius: 10.0,
                        messageTextBuilder: ((
                          message,
                          previousMessage,
                          nextMessage,
                        ) {
                          if (message.user.id == '2') {
                            return Text(
                              message.text,
                              style: const TextStyle(
                                color: Colors.black,
                                fontSize: 20,
                              ),
                            );
                          } else {
                            return Text(
                              message.text,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 20,
                              ),
                            );
                          }
                        }),
                        showTime: false,
                        containerColor: AppTheme.appleCore,
                        currentUserContainerColor: AppTheme.apricot,
                        currentUserTextColor: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(height: 10),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: controller,
                            decoration: InputDecoration(
                              labelText: 'Message',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 10),
                        IconButton(
                          onPressed: () async {
                            if (controller.text.isEmpty) return;

                            await di<ChatController>().sendMessage(
                              controller.text.trim(),
                            );
                            controller.clear();
                          },
                          icon: const Icon(
                            Icons.send,
                            color: AppTheme.blueberry,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                ],
              ),
    );
  }
}
