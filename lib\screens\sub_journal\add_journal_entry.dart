import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:watch_it/watch_it.dart';
import 'package:path_provider/path_provider.dart';

import '../../controllers/journal_contoller.dart';

class AddJournalEntry extends StatefulWidget {
  const AddJournalEntry({super.key});

  @override
  State<AddJournalEntry> createState() => _AddJournalEntryState();
}

class _AddJournalEntryState extends State<AddJournalEntry> {
  final _formKey = GlobalKey<FormState>();

  bool isSharedWithAI = false;

  final noteController = TextEditingController();

  // Image picker
  final ImagePicker _imagePicker = ImagePicker();
  XFile? image;
  String? imagePathPermanent;

  final SpeechToText _speechToText = SpeechToText();
  bool _speechEnabled = false;
  bool _isListening = false;
  String _lastWords = '';

  @override
  void initState() {
    noteController.clear();
    _initSpeech();
    super.initState();
  }

  @override
  void dispose() {
    noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(title: const Text('Add Journal Entry'), elevation: 0),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: EdgeInsets.symmetric(horizontal: 16.0),
          children: [
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: TextFormField(
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter some text';
                  }
                  return null;
                },
                controller: noteController,
                maxLines: 4,
                maxLength: 300,
                decoration: InputDecoration(
                  suffixIcon: IconButton(
                    onPressed: _speechEnabled ? speechToText : null,
                    icon: Icon(
                      _isListening ? Icons.mic : Icons.mic_none,
                      size: 30,
                      color:
                          _isListening
                              ? Colors.red
                              : (_speechEnabled
                                  ? Theme.of(context).colorScheme.primary
                                  : Colors.grey),
                    ),
                  ),
                  labelText: 'Note',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                children: [
                  Text("Share with AI", style: TextStyle(fontSize: 18)),
                  SizedBox(width: 10),
                  Switch(
                    value: isSharedWithAI,
                    onChanged: (value) {
                      setState(() {
                        isSharedWithAI = value;
                      });
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            // Image picker section
            if (imagePathPermanent != null) ...[
              // Show selected image preview
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                height: 200,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(
                      context,
                    ).colorScheme.primary.withValues(alpha: 0.3),
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(11),
                  child: Image.file(
                    File(imagePathPermanent!),
                    fit: BoxFit.cover,
                    width: double.infinity,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[200],
                        child: const Icon(
                          Icons.error,
                          color: Colors.red,
                          size: 50,
                        ),
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(height: 10),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TextButton.icon(
                    onPressed: pickImage,
                    icon: const Icon(Icons.edit),
                    label: const Text('Change Image'),
                  ),
                  const SizedBox(width: 16),
                  TextButton.icon(
                    onPressed: () {
                      setState(() {
                        image = null;
                        imagePathPermanent = null;
                      });
                    },
                    icon: const Icon(Icons.delete, color: Colors.red),
                    label: const Text(
                      'Remove',
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
            ] else ...[
              // Show image picker button
              IconButton(
                onPressed: pickImage,
                icon: Icon(
                  Icons.add_photo_alternate,
                  color: Theme.of(context).colorScheme.primary,
                  size: MediaQuery.of(context).size.width / 4,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Tap to add an image',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                textAlign: TextAlign.center,
              ),
            ],
            const SizedBox(height: 40),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: ElevatedButton(
                onPressed: () {
                  FocusManager.instance.primaryFocus?.unfocus();
                  if (_formKey.currentState!.validate()) {
                    // Save journal entry with permanent image path
                    di<JournalController>().addJournalEntry(
                      noteController.text.trim(),
                      isSharedWithAI,
                      image,
                      imagePathPermanent,
                    );
                  }
                },
                child: const Text("Save"),
              ),
            ),
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Future<void> pickImage() async {
    try {
      final XFile? pickedImage = await _imagePicker.pickImage(
        source: ImageSource.gallery,
      );

      if (pickedImage != null) {
        // Get app's permanent directory
        final Directory appDocDir = await getApplicationDocumentsDirectory();
        final Directory imageDir = Directory(
          '${appDocDir.path}/journal_images',
        );

        // Create directory if it doesn't exist
        if (!await imageDir.exists()) {
          await imageDir.create(recursive: true);
        }

        // Get original file extension
        final String originalExtension = pickedImage.path.split('.').last;
        final String fileName =
            '${DateTime.now().millisecondsSinceEpoch}.$originalExtension';
        final File permanentFile = File('${imageDir.path}/$fileName');

        // Copy the temporary file to permanent app storage
        await File(pickedImage.path).copy(permanentFile.path);

        setState(() {
          image = pickedImage;
          imagePathPermanent = permanentFile.path;
        });

        debugPrint('Image saved permanently at: $imagePathPermanent');
      }
    } catch (e) {
      debugPrint('Error saving image: $e');
      _showErrorSnackBar('Failed to save image');
    }
  }

  /// Initialize speech-to-text
  Future<void> _initSpeech() async {
    _speechEnabled = await _speechToText.initialize(
      onStatus: (status) {
        debugPrint('Speech status: $status');
        if (status == 'done' || status == 'notListening') {
          setState(() {
            _isListening = false;
          });
        }
      },
      onError: (error) {
        debugPrint('Speech error: $error');
        setState(() {
          _isListening = false;
        });
        _showErrorSnackBar('Speech recognition error: ${error.errorMsg}');
      },
    );
    setState(() {});
    debugPrint('Speech enabled: $_speechEnabled');
  }

  /// Start/stop speech recognition
  void speechToText() async {
    if (!_speechEnabled) {
      _showErrorSnackBar('Speech recognition not available');
      return;
    }

    if (_isListening) {
      // Stop listening
      await _speechToText.stop();
      setState(() {
        _isListening = false;
      });
    } else {
      // Start listening
      setState(() {
        _isListening = true;
        _lastWords = '';
      });

      await _speechToText.listen(
        onResult: (result) {
          setState(() {
            _lastWords = result.recognizedWords;
            if (result.finalResult) {
              // Append the recognized text to the existing text
              String currentText = noteController.text;
              String newText =
                  currentText.isEmpty ? _lastWords : '$currentText $_lastWords';

              // Ensure we don't exceed the max length
              if (newText.length <= 300) {
                noteController.text = newText;
                noteController.selection = TextSelection.fromPosition(
                  TextPosition(offset: noteController.text.length),
                );
              } else {
                _showErrorSnackBar('Text limit reached (300 characters)');
              }

              _isListening = false;
            }
          });
        },
        listenFor: const Duration(seconds: 30),
        pauseFor: const Duration(seconds: 3),
        localeId: 'en_US',
        listenOptions: SpeechListenOptions(
          partialResults: true,
          cancelOnError: true,
          listenMode: ListenMode.confirmation,
        ),
      );
    }
  }

  /// Show error message to user
  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
}
