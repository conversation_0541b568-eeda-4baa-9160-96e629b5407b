import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../../controllers/notifications_controller.dart';
import '../../router/rounter.dart';
import '../../services/analytics_service.dart';

class NotificationsScreen extends StatelessWidget with WatchItMixin {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final notifications = watchIt<NotificationsController>();
    return Scaffold(
      appBar: AppBar(title: const Text('Notifications'), centerTitle: true),
      body:
          notifications.isLoading
              ? const Center(child: CircularProgressIndicator())
              : Padding(
                padding: const EdgeInsets.all(16.0),
                child: ListView(
                  children: [
                    Card(
                      elevation: 1,
                      child: ListTile(
                        title: const Text(
                          'Enable Notifications',
                          style: TextStyle(fontSize: 18),
                        ),
                        trailing: Switch(
                          value: notifications.isNotificationsEnabled,
                          onChanged: (value) {
                            di<AnalyticsService>().logToggleNotifications(
                              enabled: value,
                            );
                            di<NotificationsController>().toggleNotifications(
                              value,
                            );
                          },
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),
                    const Text(
                      'Reminder Frequency',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Card(
                      elevation: 1,
                      child: Column(
                        children: [
                          RadioListTile<String>(
                            title: const Text(
                              'Every Hour',
                              style: TextStyle(fontSize: 18),
                            ),
                            value: 'hourly',
                            groupValue: notifications.hours,
                            onChanged: (value) {
                              di<NotificationsController>().setHours(value);
                            },
                          ),
                          const Divider(height: 1),
                          RadioListTile<String>(
                            title: const Text(
                              'Every 2 Hours',
                              style: TextStyle(fontSize: 18),
                            ),
                            value: 'two_hours',
                            groupValue: notifications.hours,
                            onChanged: (value) {
                              di<NotificationsController>().setHours(value);
                            },
                          ),
                          const Divider(height: 1),
                          RadioListTile<String>(
                            title: const Text(
                              'Every 4 Hours',
                              style: TextStyle(fontSize: 18),
                            ),
                            value: 'four_hours',
                            groupValue: notifications.hours,
                            onChanged: (value) {
                              di<NotificationsController>().setHours(value);
                            },
                          ),
                          const Divider(height: 1),
                          RadioListTile<String>(
                            title: const Text(
                              'Once a Day',
                              style: TextStyle(fontSize: 18),
                            ),
                            value: 'daily',
                            groupValue: notifications.hours,
                            onChanged: (value) {
                              di<NotificationsController>().setHours(value);
                            },
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                    const Text(
                      'Quiet Hours',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Card(
                      elevation: 1,
                      child: Column(
                        children: [
                          ListTile(
                            title: const Text(
                              'Start Time',
                              style: TextStyle(fontSize: 18),
                            ),
                            trailing: Text(
                              style: TextStyle(fontSize: 18),
                              notifications.formatTimeOfDay(
                                notifications.startTime,
                              ),
                            ),
                            onTap: () async {
                              final TimeOfDay? time = await showTimePicker(
                                context: context,
                                initialTime: notifications.startTime,
                              );
                              if (time != null) {
                                di<NotificationsController>().setStartTime(
                                  time,
                                );
                              }
                            },
                          ),
                          const Divider(height: 1),
                          ListTile(
                            title: const Text(
                              'End Time',
                              style: TextStyle(fontSize: 18),
                            ),
                            trailing: Text(
                              style: TextStyle(fontSize: 18),
                              notifications.formatTimeOfDay(
                                notifications.endTime,
                              ),
                            ),
                            onTap: () async {
                              final TimeOfDay? time = await showTimePicker(
                                context: context,
                                initialTime: notifications.endTime,
                              );
                              if (time != null) {
                                di<NotificationsController>().setEndTime(time);
                              }
                            },
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: 40),
                    // Save Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        onPressed: () async {
                          await di<NotificationsController>().saveInfoToPrefs();
                          router.pop();
                        },
                        child: const Text(
                          'Save Settings',
                          style: TextStyle(fontSize: 22),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
    );
  }
}
