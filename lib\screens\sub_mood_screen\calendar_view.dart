import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../../controllers/mood_controller.dart';
import '../../widgets/mood_calendar_widget.dart';
import '../../widgets/mood_card_widget.dart';

class CalendarView extends WatchingStatefulWidget {
  const CalendarView({super.key});

  @override
  State<CalendarView> createState() => _CalendarViewState();
}

class _CalendarViewState extends State<CalendarView> {
  @override
  Widget build(BuildContext context) {
    final mood = watchIt<MoodController>();
    return Scaffold(
      appBar: AppBar(title: const Text('Calendar View'), elevation: 0),
      body: ListView(
        padding: const EdgeInsets.only(top: 8, left: 8, right: 8, bottom: 80),
        children: [
          MoodCalendarWidget(
            moods: mood.moods,
            onDateSelected: (date) {
              debugPrint(date.toString());
              di<MoodController>().sortMoodsByDate(date);
            },
          ),
          ListView.builder(
            physics: NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: mood.calenderMoods.length,
            itemBuilder: (context, index) {
              return MoodCardWidget(
                index: index,
                mood: mood.calenderMoods[index],
                showDismissible: false,
              );
            },
          ),
        ],
      ),
    );
  }
}
