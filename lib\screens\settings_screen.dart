import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/theme_mode_controller.dart';
import '../theme/app_theme.dart';

class SettingsScreen extends StatelessWidget with WatchItMixin {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = watchIt<ThemeModeController>();

    return Scaffold(
      backgroundColor:
          Theme.of(context).brightness == Brightness.light
              ? AppTheme.lightBackground
              : AppTheme.darkBackground,
      appBar: AppBar(title: const Text('Settings'), elevation: 0),
      body: ListView(
        padding: const EdgeInsets.only(top: 8, left: 8, right: 8, bottom: 8),
        children: [
          Card(
            elevation: 1,
            child: ListTile(
              title: const Text(
                'Light/Dark Mode',
                style: TextStyle(fontSize: 20),
              ),
              subtitle: Text(
                'Theme : ${theme.mode}',
                style: TextStyle(fontSize: 18),
              ),
              trailing: Icon(size: 30, Icons.dark_mode),
              onTap: () async {
                debugPrint("Settings: Toggling theme mode");
                await di<ThemeModeController>().toggleMode();
                debugPrint("Settings: Theme mode toggled");
              },
            ),
          ),
          const SizedBox(height: 10),
          Card(
            elevation: 1,
            child: ListTile(
              title: const Text(
                'Notifications',
                style: TextStyle(fontSize: 20),
              ),

              trailing: Icon(size: 30, Icons.notifications),
              onTap: () async {
                context.push('/notifications');
              },
            ),
          ),
        ],
      ),
    );
  }
}
