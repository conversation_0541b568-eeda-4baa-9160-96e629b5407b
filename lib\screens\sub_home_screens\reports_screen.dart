import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../../controllers/reports_controller.dart';
import '../../theme/app_theme.dart';

class ReportsScreen extends StatelessWidget with WatchItMixin {
  const ReportsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final reports = watchIt<ReportsController>();
    return Scaffold(
      backgroundColor:
          Theme.of(context).brightness == Brightness.light
              ? AppTheme.lightBackground
              : AppTheme.darkBackground,
      appBar: AppBar(title: const Text('Reports'), centerTitle: true),
      body: const Center(child: Text('Reports Screen')),
    );
  }
}
