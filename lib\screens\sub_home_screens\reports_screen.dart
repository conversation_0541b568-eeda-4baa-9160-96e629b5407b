import 'package:flutter/material.dart';
import 'package:moodvibe/controllers/mood_controller.dart';
import 'package:watch_it/watch_it.dart';

import '../../theme/app_theme.dart';
import '../../widgets/mood_count_summary_widget.dart';
import '../../widgets/mood_line_chart_widget.dart';

class ReportsScreen extends StatelessWidget with WatchItMixin {
  const ReportsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final reports = watchIt<MoodController>();
    return Scaffold(
      backgroundColor:
          Theme.of(context).brightness == Brightness.light
              ? AppTheme.lightBackground
              : AppTheme.darkBackground,
      appBar: AppBar(title: const Text('Reports'), centerTitle: true),
      body: ListView(
        padding: const EdgeInsets.only(
          top: 16,
          left: 16,
          right: 16,
          bottom: 80,
        ),
        children: [
          // In your reports screen or any other screen
          MoodLineChartWidget(
            moods: reports.moods,
            daysToShow: 10, // Optional, defaults to 10
          ),
          const SizedBox(height: 10),
          MoodCountSummaryWidget(moods: reports.moods),
        ],
      ),
    );
  }
}
