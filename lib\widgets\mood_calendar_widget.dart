import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../models/mood.dart';
import '../theme/app_theme.dart';

class MoodCalendarWidget extends StatefulWidget {
  final List<MoodModel> moods;
  final Function(DateTime)? onDateSelected;

  const MoodCalendarWidget({
    super.key,
    required this.moods,
    this.onDateSelected,
  });

  @override
  State<MoodCalendarWidget> createState() => _MoodCalendarWidgetState();
}

class _MoodCalendarWidgetState extends State<MoodCalendarWidget> {
  late DateTime currentMonth;
  late DateTime firstMoodDate;
  late DateTime lastMoodDate;

  @override
  void initState() {
    super.initState();
    currentMonth = DateTime(DateTime.now().year, DateTime.now().month);
    _initializeDateRange();
  }

  void _initializeDateRange() {
    final moods = widget.moods;

    if (moods.isNotEmpty) {
      // Sort moods by date to find first and last
      final sortedMoods = List<MoodModel>.from(moods)..sort(
        (a, b) => (a.createdAt ?? DateTime.now()).compareTo(
          b.createdAt ?? DateTime.now(),
        ),
      );

      firstMoodDate = DateTime(
        (sortedMoods.first.createdAt ?? DateTime.now()).year,
        (sortedMoods.first.createdAt ?? DateTime.now()).month,
      );
      lastMoodDate = DateTime(DateTime.now().year, DateTime.now().month);
    } else {
      // If no moods, default to current month
      firstMoodDate = currentMonth;
      lastMoodDate = currentMonth;
    }
  }

  void _previousMonth() {
    if (currentMonth.isAfter(firstMoodDate)) {
      setState(() {
        currentMonth = DateTime(currentMonth.year, currentMonth.month - 1);
      });
    }
  }

  void _nextMonth() {
    if (currentMonth.isBefore(lastMoodDate)) {
      setState(() {
        currentMonth = DateTime(currentMonth.year, currentMonth.month + 1);
      });
    }
  }

  Map<int, int> _getMoodCountsForMonth() {
    final moods = widget.moods;
    final Map<int, int> moodCounts = {};

    for (final mood in moods) {
      final moodDate = mood.createdAt ?? DateTime.now();
      if (moodDate.year == currentMonth.year &&
          moodDate.month == currentMonth.month) {
        final day = moodDate.day;
        moodCounts[day] = (moodCounts[day] ?? 0) + 1;
      }
    }

    return moodCounts;
  }

  @override
  Widget build(BuildContext context) {
    final moodCounts = _getMoodCountsForMonth();

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with navigation
          _buildHeader(),
          const SizedBox(height: 20),

          // Weekday labels
          _buildWeekdayLabels(),
          const SizedBox(height: 10),

          // Calendar grid
          _buildCalendarGrid(moodCounts),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    final monthNames = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
          onPressed:
              currentMonth.isAfter(firstMoodDate) ? _previousMonth : null,
          icon: Icon(
            Icons.chevron_left,
            color:
                currentMonth.isAfter(firstMoodDate)
                    ? AppTheme.blueberry
                    : Colors.grey,
          ),
        ),
        Text(
          '${monthNames[currentMonth.month - 1]} ${currentMonth.year}',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.blueberry,
          ),
        ),
        IconButton(
          onPressed: currentMonth.isBefore(lastMoodDate) ? _nextMonth : null,
          icon: Icon(
            Icons.chevron_right,
            color:
                currentMonth.isBefore(lastMoodDate)
                    ? AppTheme.blueberry
                    : Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildWeekdayLabels() {
    const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

    return Row(
      children:
          weekdays
              .map(
                (day) => Expanded(
                  child: Center(
                    child: Text(
                      day,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.blueberry.withValues(alpha: 0.7),
                      ),
                    ),
                  ),
                ),
              )
              .toList(),
    );
  }

  Widget _buildCalendarGrid(Map<int, int> moodCounts) {
    final firstDayOfMonth = DateTime(currentMonth.year, currentMonth.month, 1);
    final lastDayOfMonth = DateTime(
      currentMonth.year,
      currentMonth.month + 1,
      0,
    );
    final firstWeekday = firstDayOfMonth.weekday % 7; // Sunday = 0
    final daysInMonth = lastDayOfMonth.day;
    final totalCells = firstWeekday + daysInMonth;
    final rows = (totalCells / 7).ceil();

    return SizedBox(
      height:
          rows *
          60.0, // Fixed height based on number of rows (increased for proper spacing)
      child: GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 7,
          childAspectRatio: 1,
          crossAxisSpacing: 4,
          mainAxisSpacing: 4,
        ),
        itemCount: totalCells,
        itemBuilder: (context, index) {
          if (index < firstWeekday) {
            // Empty cells before the first day of the month
            return const SizedBox();
          }

          final day = index - firstWeekday + 1;
          final moodCount = moodCounts[day] ?? 0;
          final hasData = moodCount > 0;

          // Check if this day is in the future
          final dayDate = DateTime(currentMonth.year, currentMonth.month, day);
          final today = DateTime.now();
          final todayDate = DateTime(today.year, today.month, today.day);
          final isFutureDate = dayDate.isAfter(todayDate);

          return _buildDayCell(day, moodCount, hasData, isFutureDate);
        },
      ),
    );
  }

  Widget _buildDayCell(
    int day,
    int moodCount,
    bool hasData,
    bool isFutureDate,
  ) {
    return InkWell(
      onTap: () {
        final selectedDate = DateTime(
          currentMonth.year,
          currentMonth.month,
          day,
        );

        // Always call onDateSelected to show moods for this date
        widget.onDateSelected?.call(selectedDate);

        // Only navigate to add mood if it's not a future date and has no data
        if (!hasData && !isFutureDate) {
          context.push('/addMood?date=${selectedDate.millisecondsSinceEpoch}');
        }
      },
      borderRadius: BorderRadius.circular(25),
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: AppTheme.blueberry.withValues(alpha: 0.7),
            width: 1,
          ),
          color:
              hasData
                  ? AppTheme.blueberry.withValues(alpha: 0.1)
                  : Colors.transparent,
        ),
        child: Stack(
          children: [
            // Day number in center (always shown)
            Center(
              child: Text(
                day.toString(),
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  color:
                      isFutureDate
                          ? AppTheme.blueberry.withValues(alpha: 0.2)
                          : hasData
                          ? AppTheme.blueberry
                          : AppTheme.blueberry.withValues(alpha: 0.5),
                  fontSize: 16,
                ),
              ),
            ),

            // Top-right indicator
            Positioned(
              top: 4,
              right: 4,
              child:
                  hasData
                      ? Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppTheme.citrus,
                        ),
                        child: Center(
                          child: Text(
                            moodCount.toString(),
                            style: const TextStyle(
                              color: AppTheme.blueberry,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      )
                      : !isFutureDate
                      ? Icon(Icons.add, color: AppTheme.apricot, size: 18)
                      : const SizedBox(), // No icon for future dates
            ),
          ],
        ),
      ),
    );
  }
}
