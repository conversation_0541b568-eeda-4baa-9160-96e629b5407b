{"buildFiles": ["C:\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\WORKING-ON\\APPS\\V7\\moodvibe\\android\\app\\.cxx\\Debug\\4r4519q4\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\WORKING-ON\\APPS\\V7\\moodvibe\\android\\app\\.cxx\\Debug\\4r4519q4\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}