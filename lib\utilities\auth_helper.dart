import 'package:watch_it/watch_it.dart';
import '../controllers/auth_controller.dart';

/// Utility class for common authentication operations
class AuthHelper {
  /// Get the current logged-in user's ID
  /// Returns null if no user is logged in
  static String? getCurrentUserId() {
    return di<AuthController>().getCurrentUserId();
  }

  /// Check if a user is currently logged in
  static bool isLoggedIn() {
    return di<AuthController>().isLoggedIn();
  }

  /// Get the current user's email
  /// Returns null if no user is logged in
  static String? getCurrentUserEmail() {
    return di<AuthController>().getCurrentUser()?.email;
  }

  /// Get the current user's display name
  /// Returns null if no user is logged in or no display name is set
  static String? getCurrentUserDisplayName() {
    return di<AuthController>().getCurrentUser()?.displayName;
  }

  /// Require user to be logged in, throws exception if not
  /// Returns the user ID if logged in
  static String requireCurrentUserId() {
    final userId = getCurrentUserId();
    if (userId == null) {
      throw Exception('User must be logged in to perform this action');
    }
    return userId;
  }
}
