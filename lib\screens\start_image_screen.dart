import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class StartImageScreen extends StatelessWidget {
  const StartImageScreen({super.key});

  @override
  Widget build(BuildContext context) {
    Future.delayed(const Duration(milliseconds: 600), () async {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser != null) {
        // User is logged in, navigate to main screen
        if (context.mounted) {
          context.go('/mainScreen');
        }
      } else {
        // User is not logged in, navigate to register screen
        if (context.mounted) {
          context.go('/login');
        }
      }
    });

    return Scaffold(
      body: Center(
        child: Image.asset(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          'assets/images/loading_screen.png',
          fit: BoxFit.cover,
        ),
      ),
    );
  }
}
