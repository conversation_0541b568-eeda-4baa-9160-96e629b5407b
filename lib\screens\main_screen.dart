import 'package:flutter/material.dart';
import 'package:moodvibe/theme/app_theme.dart';
import 'package:watch_it/watch_it.dart';
import '../services/analytics_service.dart';
import 'four_screens/chat_screen.dart';
import 'four_screens/home_screen.dart';
import 'four_screens/jounal_screen.dart';
import 'four_screens/mood_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentPage = 0;
  final PageController _pageController = PageController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView(
        physics: const NeverScrollableScrollPhysics(),
        controller: _pageController,
        onPageChanged: (value) {
          debugPrint(value.toString());
          setState(() {
            _currentPage = value;
          });
        },
        children: [HomeScreen(), <PERSON><PERSON><PERSON><PERSON><PERSON>(), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(), Cha<PERSON><PERSON><PERSON><PERSON>()],
      ),
      bottomNavigationBar: BottomNavigationBar(
        backgroundColor:
            Theme.of(context).bottomNavigationBarTheme.backgroundColor,
        selectedItemColor: AppTheme.apricot,
        unselectedItemColor:
            Theme.of(context).bottomNavigationBarTheme.unselectedItemColor,
        elevation: 8,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home_outlined),
            label: 'Home',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.mood), label: 'Mood'),
          BottomNavigationBarItem(
            icon: Icon(Icons.note_outlined),
            label: 'Journal',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.chat_bubble_outline),
            label: 'Chat',
          ),
        ],
        onTap: (page) {
          if (page != _currentPage) {
            setState(() {
              _currentPage = page;
              _pageController.jumpToPage(page);
            });
          }
          // Log screen view
          switch (page) {
            case 0:
              di<AnalyticsService>().logScreenView(screenName: 'Home Screen');
              break;
            case 1:
              di<AnalyticsService>().logScreenView(screenName: 'Mood Screen');
              break;
            case 2:
              di<AnalyticsService>().logScreenView(
                screenName: 'Journal Screen',
              );
              break;
            case 3:
              di<AnalyticsService>().logScreenView(screenName: 'Chat Screen');
              break;
            default:
              break;
          }
        },
        currentIndex: _currentPage,
      ),
    );
  }
}
