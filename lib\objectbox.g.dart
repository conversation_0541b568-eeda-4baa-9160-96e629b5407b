// GENERATED CODE - DO NOT MODIFY BY HAND
// This code was generated by ObjectBox. To update it run the generator again
// with `dart run build_runner build`.
// See also https://docs.objectbox.io/getting-started#generate-objectbox-code

// ignore_for_file: camel_case_types, depend_on_referenced_packages
// coverage:ignore-file

import 'dart:typed_data';

import 'package:flat_buffers/flat_buffers.dart' as fb;
import 'package:objectbox/internal.dart'
    as obx_int; // generated code can access "internal" functionality
import 'package:objectbox/objectbox.dart' as obx;
import 'package:objectbox_flutter_libs/objectbox_flutter_libs.dart';

import 'models/activities_model.dart';
import 'models/jounal.dart';
import 'models/mood.dart';

export 'package:objectbox/objectbox.dart'; // so that callers only have to import this file

final _entities = <obx_int.ModelEntity>[
  obx_int.ModelEntity(
    id: const obx_int.IdUid(1, 9000364630673312956),
    name: '<PERSON>od<PERSON><PERSON><PERSON>',
    lastPropertyId: const obx_int.IdUid(6, 1236734736256767476),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 4586764448619901744),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 9215844035464463252),
        name: 'rating',
        type: 8,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 886823196631613259),
        name: 'createdAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 2472737900249217245),
        name: 'mood',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 2486028341136845551),
        name: 'isSharedWithAI',
        type: 1,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 1236734736256767476),
        name: 'note',
        type: 9,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(2, 1270810904858355715),
    name: 'JournalModel',
    lastPropertyId: const obx_int.IdUid(5, 7217687718156760149),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 9138581968482764788),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 4466694816372378779),
        name: 'note',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 8781107326323211266),
        name: 'createdAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 5854871498611686338),
        name: 'isSharedWithAI',
        type: 1,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 7217687718156760149),
        name: 'imagePath',
        type: 9,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(3, 2067554371463491883),
    name: 'ActivitiesModel',
    lastPropertyId: const obx_int.IdUid(2, 8821550101412439556),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 245324690174407564),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 8821550101412439556),
        name: 'activityName',
        type: 9,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
];

/// Shortcut for [obx.Store.new] that passes [getObjectBoxModel] and for Flutter
/// apps by default a [directory] using `defaultStoreDirectory()` from the
/// ObjectBox Flutter library.
///
/// Note: for desktop apps it is recommended to specify a unique [directory].
///
/// See [obx.Store.new] for an explanation of all parameters.
///
/// For Flutter apps, also calls `loadObjectBoxLibraryAndroidCompat()` from
/// the ObjectBox Flutter library to fix loading the native ObjectBox library
/// on Android 6 and older.
Future<obx.Store> openStore({
  String? directory,
  int? maxDBSizeInKB,
  int? maxDataSizeInKB,
  int? fileMode,
  int? maxReaders,
  bool queriesCaseSensitiveDefault = true,
  String? macosApplicationGroup,
}) async {
  await loadObjectBoxLibraryAndroidCompat();
  return obx.Store(
    getObjectBoxModel(),
    directory: directory ?? (await defaultStoreDirectory()).path,
    maxDBSizeInKB: maxDBSizeInKB,
    maxDataSizeInKB: maxDataSizeInKB,
    fileMode: fileMode,
    maxReaders: maxReaders,
    queriesCaseSensitiveDefault: queriesCaseSensitiveDefault,
    macosApplicationGroup: macosApplicationGroup,
  );
}

/// Returns the ObjectBox model definition for this project for use with
/// [obx.Store.new].
obx_int.ModelDefinition getObjectBoxModel() {
  final model = obx_int.ModelInfo(
    entities: _entities,
    lastEntityId: const obx_int.IdUid(3, 2067554371463491883),
    lastIndexId: const obx_int.IdUid(0, 0),
    lastRelationId: const obx_int.IdUid(0, 0),
    lastSequenceId: const obx_int.IdUid(0, 0),
    retiredEntityUids: const [],
    retiredIndexUids: const [],
    retiredPropertyUids: const [],
    retiredRelationUids: const [],
    modelVersion: 5,
    modelVersionParserMinimum: 5,
    version: 1,
  );

  final bindings = <Type, obx_int.EntityDefinition>{
    MoodModel: obx_int.EntityDefinition<MoodModel>(
      model: _entities[0],
      toOneRelations: (MoodModel object) => [],
      toManyRelations: (MoodModel object) => {},
      getId: (MoodModel object) => object.id,
      setId: (MoodModel object, int id) {
        object.id = id;
      },
      objectToFB: (MoodModel object, fb.Builder fbb) {
        final moodOffset = object.mood == null
            ? null
            : fbb.writeString(object.mood!);
        final noteOffset = object.note == null
            ? null
            : fbb.writeString(object.note!);
        fbb.startTable(7);
        fbb.addInt64(0, object.id);
        fbb.addFloat64(1, object.rating);
        fbb.addInt64(2, object.createdAt?.millisecondsSinceEpoch);
        fbb.addOffset(3, moodOffset);
        fbb.addBool(4, object.isSharedWithAI);
        fbb.addOffset(5, noteOffset);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final createdAtValue = const fb.Int64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          8,
        );
        final ratingParam = const fb.Float64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          6,
        );
        final createdAtParam = createdAtValue == null
            ? null
            : DateTime.fromMillisecondsSinceEpoch(createdAtValue);
        final moodParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 10);
        final isSharedWithAIParam = const fb.BoolReader().vTableGet(
          buffer,
          rootOffset,
          12,
          false,
        );
        final noteParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 14);
        final object = MoodModel(
          rating: ratingParam,
          createdAt: createdAtParam,
          mood: moodParam,
          isSharedWithAI: isSharedWithAIParam,
          note: noteParam,
        )..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

        return object;
      },
    ),
    JournalModel: obx_int.EntityDefinition<JournalModel>(
      model: _entities[1],
      toOneRelations: (JournalModel object) => [],
      toManyRelations: (JournalModel object) => {},
      getId: (JournalModel object) => object.id,
      setId: (JournalModel object, int id) {
        object.id = id;
      },
      objectToFB: (JournalModel object, fb.Builder fbb) {
        final noteOffset = fbb.writeString(object.note);
        final imagePathOffset = object.imagePath == null
            ? null
            : fbb.writeString(object.imagePath!);
        fbb.startTable(6);
        fbb.addInt64(0, object.id);
        fbb.addOffset(1, noteOffset);
        fbb.addInt64(2, object.createdAt?.millisecondsSinceEpoch);
        fbb.addBool(3, object.isSharedWithAI);
        fbb.addOffset(4, imagePathOffset);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final createdAtValue = const fb.Int64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          8,
        );
        final noteParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final createdAtParam = createdAtValue == null
            ? null
            : DateTime.fromMillisecondsSinceEpoch(createdAtValue);
        final isSharedWithAIParam = const fb.BoolReader().vTableGet(
          buffer,
          rootOffset,
          10,
          false,
        );
        final imagePathParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 12);
        final object = JournalModel(
          note: noteParam,
          createdAt: createdAtParam,
          isSharedWithAI: isSharedWithAIParam,
          imagePath: imagePathParam,
        )..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

        return object;
      },
    ),
    ActivitiesModel: obx_int.EntityDefinition<ActivitiesModel>(
      model: _entities[2],
      toOneRelations: (ActivitiesModel object) => [],
      toManyRelations: (ActivitiesModel object) => {},
      getId: (ActivitiesModel object) => object.id,
      setId: (ActivitiesModel object, int id) {
        object.id = id;
      },
      objectToFB: (ActivitiesModel object, fb.Builder fbb) {
        final activityNameOffset = fbb.writeString(object.activityName);
        fbb.startTable(3);
        fbb.addInt64(0, object.id);
        fbb.addOffset(1, activityNameOffset);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final activityNameParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final object = ActivitiesModel(activityName: activityNameParam)
          ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

        return object;
      },
    ),
  };

  return obx_int.ModelDefinition(model, bindings);
}

/// [MoodModel] entity fields to define ObjectBox queries.
class MoodModel_ {
  /// See [MoodModel.id].
  static final id = obx.QueryIntegerProperty<MoodModel>(
    _entities[0].properties[0],
  );

  /// See [MoodModel.rating].
  static final rating = obx.QueryDoubleProperty<MoodModel>(
    _entities[0].properties[1],
  );

  /// See [MoodModel.createdAt].
  static final createdAt = obx.QueryDateProperty<MoodModel>(
    _entities[0].properties[2],
  );

  /// See [MoodModel.mood].
  static final mood = obx.QueryStringProperty<MoodModel>(
    _entities[0].properties[3],
  );

  /// See [MoodModel.isSharedWithAI].
  static final isSharedWithAI = obx.QueryBooleanProperty<MoodModel>(
    _entities[0].properties[4],
  );

  /// See [MoodModel.note].
  static final note = obx.QueryStringProperty<MoodModel>(
    _entities[0].properties[5],
  );
}

/// [JournalModel] entity fields to define ObjectBox queries.
class JournalModel_ {
  /// See [JournalModel.id].
  static final id = obx.QueryIntegerProperty<JournalModel>(
    _entities[1].properties[0],
  );

  /// See [JournalModel.note].
  static final note = obx.QueryStringProperty<JournalModel>(
    _entities[1].properties[1],
  );

  /// See [JournalModel.createdAt].
  static final createdAt = obx.QueryDateProperty<JournalModel>(
    _entities[1].properties[2],
  );

  /// See [JournalModel.isSharedWithAI].
  static final isSharedWithAI = obx.QueryBooleanProperty<JournalModel>(
    _entities[1].properties[3],
  );

  /// See [JournalModel.imagePath].
  static final imagePath = obx.QueryStringProperty<JournalModel>(
    _entities[1].properties[4],
  );
}

/// [ActivitiesModel] entity fields to define ObjectBox queries.
class ActivitiesModel_ {
  /// See [ActivitiesModel.id].
  static final id = obx.QueryIntegerProperty<ActivitiesModel>(
    _entities[2].properties[0],
  );

  /// See [ActivitiesModel.activityName].
  static final activityName = obx.QueryStringProperty<ActivitiesModel>(
    _entities[2].properties[1],
  );
}
