import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../repositories/i_add_jounal_local_db.dart';
import '../repositories/i_add_mood_local_db.dart';
import '../services/analytics_service.dart';
import 'journal_contoller.dart';
import 'mood_controller.dart';

class SettingsController extends ChangeNotifier {
  Future<void> deleteAllData() async {
    await di<IAddMoodLocalDb>().deleteAllMoods();
    await di<IAddJournalLocalDb>().deleteAllJournals();

    await di<AnalyticsService>().logEvent(
      name: 'delete_all_data',
      parameters: {},
    );

    await di<MoodController>().init();
    await di<JournalController>().init();

    notifyListeners();
  }
}
