import 'package:shared_preferences/shared_preferences.dart';
import 'i_notifications_prefs.dart';

class NotificationsPrefs implements INotificationsPrefs {
  @override
  Future<void> saveNotificationsToPrefs(
    String hours,
    String startTime,
    String endTime,
    bool isNotificationsEnabled,
  ) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    await prefs.setString('hours', hours);
    await prefs.setString('startTime', startTime);
    await prefs.setString('endTime', endTime);
    await prefs.setBool('isNotificationsEnabled', isNotificationsEnabled);
  }

  @override
  Future<
    ({
      String hours,
      String startTime,
      String endTime,
      bool isNotificationsEnabled,
    })
  >
  getNotificationsFromPrefs() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return (
      hours: prefs.getString('hours') ?? 'hourly',
      startTime: prefs.getString('startTime') ?? '22:00',
      endTime: prefs.getString('endTime') ?? '07:00',
      isNotificationsEnabled: prefs.getBool('isNotificationsEnabled') ?? false,
    );
  }
}
