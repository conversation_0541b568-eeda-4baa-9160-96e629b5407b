import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../../controllers/self_help_tips_controller.dart';
import '../../theme/app_theme.dart';

class SelfHelpTipsScreen extends StatelessWidget with WatchItMixin {
  const SelfHelpTipsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final self = watchIt<SelfHelpTipsController>();

    return Scaffold(
      appBar: AppBar(title: const Text('Self Help Tips'), elevation: 0),
      body: ListView.builder(
        
        itemCount: self.selfHelpTips.length,
        padding: const EdgeInsets.only(top: 8, left: 16, right: 16, bottom: 80),
        itemBuilder: (context, index) {
          final tip = self.selfHelpTips[index];
          return SelfHelpCard(
            title: tip.data?.title ?? '',
            description: tip.desc ?? '',
          );
        },
      ),
    );
  }
}

class SelfHelpCard extends StatelessWidget {
  final String title;
  final String description;

  const SelfHelpCard({
    required this.title,
    required this.description,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.citrus.withValues(alpha: 0.1),
            AppTheme.appleCore.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.blueberry.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.blueberry.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 10),
            AutoSizeText(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.blueberry,
              ),
              textAlign: TextAlign.start,
            ),
            const SizedBox(height: 10),
            AutoSizeText(
              description,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.8),
                height: 1.5,
              ),
              textAlign: TextAlign.justify,
            ),
          ],
        ),
      ),
    );
  }
}
