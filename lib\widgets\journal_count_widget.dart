import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';

import '../models/jounal.dart';
import '../theme/app_theme.dart';

class JournalCountWidget extends StatelessWidget {
  final List<JournalModel> journals;

  const JournalCountWidget({super.key, required this.journals});

  @override
  Widget build(BuildContext context) {
    final counts = _calculateJournalCounts();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.citrus.withValues(alpha: 0.1),
            AppTheme.appleCore.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.blueberry.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Journal Entries',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.blueberry,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildJournalCountCircle(
                context,
                counts['withImage']!,
                'With Image',
                AppTheme.citrus,
              ),
              _buildJournalCountCircle(
                context,
                counts['withoutImage']!,
                'Without Image',
                AppTheme.apricot,
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildTotalCount(context),
        ],
      ),
    );
  }

  Widget _buildJournalCountCircle(
    BuildContext context,
    int count,
    String label,
    Color color,
  ) {
    return Expanded(
      child: Column(
        children: [
          // Circle with count
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  color.withValues(alpha: 0.8),
                  color.withValues(alpha: 0.6),
                ],
              ),
              border: Border.all(color: color, width: 2),
              boxShadow: [
                BoxShadow(
                  color: color.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 4),
                AutoSizeText(
                  count.toString(),
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 26,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),

          // Label below circle
          Text(
            label,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalCount(BuildContext context) {
    final totalJournals = journals.length;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: AppTheme.blueberry.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.blueberry.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.book_outlined, color: AppTheme.blueberry, size: 20),
          const SizedBox(width: 8),
          Text(
            'Total Journal Entries: ',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.blueberry.withValues(alpha: 0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            totalJournals.toString(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.blueberry,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Map<String, int> _calculateJournalCounts() {
    int withImage = 0;
    int withoutImage = 0;

    for (final journal in journals) {
      if (journal.imagePath != null && journal.imagePath!.isNotEmpty) {
        withImage++;
      } else {
        withoutImage++;
      }
    }

    return {'withImage': withImage, 'withoutImage': withoutImage};
  }
}
