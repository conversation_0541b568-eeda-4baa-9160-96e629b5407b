import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../models/self_help_tips.dart';
import '../repositories/i_backend_api.dart';

class SelfHelpTipsController extends ChangeNotifier {
  SelfHelpTipsController() {
    init();
  }

  List<SelfHelpTips> _selfHelpTips = [];

  List<SelfHelpTips> get selfHelpTips => _selfHelpTips;

  Future<void> init() async {
    getSelfHelpTips();
  }

  Future<void> getSelfHelpTips() async {
    // Fetch self-help tips from API or local data source
    try {
      final response = await di<IBackendApi>().getSelfHelpTips();
      if (response.statusCode == 200) {
        // Extract the 'data' field from the response which contains the list
        final responseData = response.data as Map<String, dynamic>;
        final tipsData = responseData['data'] as List;

        _selfHelpTips =
            tipsData
                .map(
                  (json) => SelfHelpTips.fromJson(json as Map<String, dynamic>),
                )
                .toList();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error fetching self-help tips: $e');
    }
  }
}
