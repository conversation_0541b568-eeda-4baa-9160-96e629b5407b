import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../models/self_help_tips.dart';
import '../repositories/i_backend_api.dart';

class SelfHelpTipsController extends ChangeNotifier {
  SelfHelpTipsController() {
    init();
  }

  List<SelfHelpTips> selfHelpTips = [];

  Future<void> init() async {
    // Initialize any necessary data or perform any setup tasks
  }

  Future<void> getSelfHelpTips() async {
    // Fetch self-help tips from API or local data source
    try {
      final response = await di<IBackendApi>().getSelfHelpTips();
      if (response.statusCode == 200) {
        selfHelpTips =
            (response.data as List)
                .map((json) => SelfHelpTips.fromJson(json))
                .toList();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error fetching self-help tips: $e');
    }
  }
}
