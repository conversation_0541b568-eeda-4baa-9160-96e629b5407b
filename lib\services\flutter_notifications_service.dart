import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

class FlutterNotificationsService {
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
  bool _initialized = false;

  // Initialize notifications
  Future<void> init() async {
    if (_initialized) return;

    tz.initializeTimeZones();

    // Initialize settings for all platforms
    const InitializationSettings initializationSettings =
        InitializationSettings(
          android: AndroidInitializationSettings('@mipmap/ic_launcher'),
          iOS: DarwinInitializationSettings(
            // Set to true to request permissions during initialization
            // This is important for iOS to show the permission dialog
            requestAlertPermission: true,
            requestBadgePermission: true,
            requestSoundPermission: true,
            // Add these settings to ensure iOS notifications work properly
            defaultPresentAlert: true,
            defaultPresentBadge: true,
            defaultPresentSound: true,
          ),
        );

    // Initialize the plugin
    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (NotificationResponse details) {
        // Handle notification click
        debugPrint('Notification clicked: ${details.payload}');
      },
    );

    _initialized = true;
  }

  // Request notification permissions - simplified approach
  Future<bool> requestPermissions() async {
    try {
      // For iOS, request permissions directly
      if (Platform.isIOS) {
        // Use the iOS plugin to request permissions
        final bool? result = await _flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin
            >()
            ?.requestPermissions(alert: true, badge: true, sound: true);
        debugPrint('iOS permission request result: $result');
        return result ?? false;
      }

      // For Android, request permissions directly
      if (Platform.isAndroid) {
        final bool? result =
            await _flutterLocalNotificationsPlugin
                .resolvePlatformSpecificImplementation<
                  AndroidFlutterLocalNotificationsPlugin
                >()
                ?.requestNotificationsPermission();
        debugPrint('Android permission request result: $result');
        return result ?? false;
      }

      return true;
    } catch (e) {
      debugPrint('Error requesting permissions: $e');
      return false;
    }
  }

  // Check and request exact alarm permissions (Android 12+)
  Future<bool> checkExactAlarmPermission() async {
    try {
      if (Platform.isAndroid) {
        final bool? canScheduleExactAlarms =
            await _flutterLocalNotificationsPlugin
                .resolvePlatformSpecificImplementation<
                  AndroidFlutterLocalNotificationsPlugin
                >()
                ?.canScheduleExactNotifications();

        debugPrint('Can schedule exact alarms: $canScheduleExactAlarms');
        return canScheduleExactAlarms ?? false;
      }
      return true; // iOS doesn't have this restriction
    } catch (e) {
      debugPrint('Error checking exact alarm permission: $e');
      return false;
    }
  }

  // Request exact alarm permissions (Android 12+)
  Future<bool> requestExactAlarmPermission() async {
    try {
      if (Platform.isAndroid) {
        final bool? result =
            await _flutterLocalNotificationsPlugin
                .resolvePlatformSpecificImplementation<
                  AndroidFlutterLocalNotificationsPlugin
                >()
                ?.requestExactAlarmsPermission();

        debugPrint('Exact alarm permission request result: $result');
        return result ?? false;
      }
      return true; // iOS doesn't need this
    } catch (e) {
      debugPrint('Error requesting exact alarm permission: $e');
      return false;
    }
  }

  // Check if notifications are enabled - simplified approach
  Future<bool> areNotificationsEnabled() async {
    try {
      // For Android, check if notifications are enabled
      if (Platform.isAndroid) {
        final bool? result =
            await _flutterLocalNotificationsPlugin
                .resolvePlatformSpecificImplementation<
                  AndroidFlutterLocalNotificationsPlugin
                >()
                ?.areNotificationsEnabled();

        if (result != null) {
          return result;
        }
      }

      // For iOS, we'll check by requesting permissions
      // This won't show a dialog if permissions were already decided
      if (Platform.isIOS) {
        final bool? result = await _flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin
            >()
            ?.requestPermissions(alert: true, badge: true, sound: true);
        if (result != null) {
          return result;
        }
      }

      // If platform-specific checks fail, check pending notifications
      final List<PendingNotificationRequest> pendingNotifications =
          await _flutterLocalNotificationsPlugin.pendingNotificationRequests();

      // If we have pending notifications, assume notifications are enabled
      return pendingNotifications.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking if notifications are enabled: $e');
      return false;
    }
  }

  // Cancel all notifications
  Future<void> cancelAllNotifications() async {
    await _flutterLocalNotificationsPlugin.cancelAll();
  }

  // Schedule a notification
  Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
  }) async {
    final AndroidNotificationDetails androidDetails =
        const AndroidNotificationDetails(
          'mood_reminder_channel',
          'Mood Reminder Notifications',
          channelDescription: 'Notifications for enter mood reminders',
          importance: Importance.high,
          priority: Priority.high,
          showWhen: true,
        );

    final DarwinNotificationDetails iOSDetails =
        const DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
          sound: 'default', // Use the default sound
          interruptionLevel:
              InterruptionLevel.active, // Make notifications more prominent
          threadIdentifier: 'mood_reminder', // Group notifications
        );

    final NotificationDetails notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iOSDetails,
    );

    final tz.TZDateTime scheduledTZDate = tz.TZDateTime.from(
      scheduledDate,
      tz.local,
    );

    try {
      await _flutterLocalNotificationsPlugin.zonedSchedule(
        id,
        title,
        body,
        scheduledTZDate,
        notificationDetails,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
      );
    } catch (e) {
      // If exact scheduling fails, fall back to inexact scheduling
      debugPrint('Exact scheduling failed, falling back to inexact: $e');
      await _flutterLocalNotificationsPlugin.zonedSchedule(
        id,
        title,
        body,
        scheduledTZDate,
        notificationDetails,
        androidScheduleMode: AndroidScheduleMode.inexactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
      );
    }
  }
}
