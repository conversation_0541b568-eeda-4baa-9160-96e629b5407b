// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDpfkYbfJ-zc4ijdmj7S0pKpbLA2bpNTpQ',
    appId: '1:349516333175:web:acd042f432510962b90672',
    messagingSenderId: '349516333175',
    projectId: 'moodvibe-c414e',
    authDomain: 'moodvibe-c414e.firebaseapp.com',
    storageBucket: 'moodvibe-c414e.firebasestorage.app',
    measurementId: 'G-VSBP5HRD5B',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyACnPYwWHUg-S4zYFCgP35EruYH-d9mj9c',
    appId: '1:349516333175:android:62874e133cd08853b90672',
    messagingSenderId: '349516333175',
    projectId: 'moodvibe-c414e',
    storageBucket: 'moodvibe-c414e.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBmzvUV9K0CRg1XpxeUzCwCEW4ucjPxg6o',
    appId: '1:349516333175:ios:9644285a54a95c85b90672',
    messagingSenderId: '349516333175',
    projectId: 'moodvibe-c414e',
    storageBucket: 'moodvibe-c414e.firebasestorage.app',
    iosBundleId: 'com.dextersoftware.moodvibe',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBmzvUV9K0CRg1XpxeUzCwCEW4ucjPxg6o',
    appId: '1:349516333175:ios:9644285a54a95c85b90672',
    messagingSenderId: '349516333175',
    projectId: 'moodvibe-c414e',
    storageBucket: 'moodvibe-c414e.firebasestorage.app',
    iosBundleId: 'com.dextersoftware.moodvibe',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDpfkYbfJ-zc4ijdmj7S0pKpbLA2bpNTpQ',
    appId: '1:349516333175:web:73bfa8dc66e7ca64b90672',
    messagingSenderId: '349516333175',
    projectId: 'moodvibe-c414e',
    authDomain: 'moodvibe-c414e.firebaseapp.com',
    storageBucket: 'moodvibe-c414e.firebasestorage.app',
    measurementId: 'G-H8LF5X1E4T',
  );
}
