import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:moodvibe/config/server_config.dart';

import '../config/app_keys.dart';
import 'i_backend_api.dart';

class BackendApi implements IBackendApi {
  final Dio _dio = Dio();
  final String _baseUrl = ServerConfig.serverUrl;
  final String _bearerToken = AppKeys.backendApiKey;

  BackendApi() {
    // Configure Dio to handle JSON properly
    _dio.options.responseType = ResponseType.json;
    _dio.options.sendTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
  }

  @override
  Future<Response> addMood(
    String note,
    bool isSharedWithAI,
    double rating,
    String mood,
    String userId,
  ) async {
    try {
      debugPrint('API URL: $_baseUrl/add-mood');
      debugPrint('Bearer Token: $_bearerToken');

      final response = await _dio.post(
        '$_baseUrl/add-mood',
        options: Options(
          headers: {
            'Authorization': 'Bearer $_bearerToken',
            'Content-Type': 'application/json; charset=utf-8',
          },
          contentType: 'application/json',
        ),
        data: {
          "note": note.toString(),
          "isSharedWithAI": isSharedWithAI,
          "rating": rating.toInt(), // Convert double to int
          "mood": mood.toString(),
          "userId": userId.toString(),
        },
      );

      debugPrint('API Response: ${response.statusCode} - ${response.data}');
      return response;
    } catch (e) {
      debugPrint('Error adding mood to API: $e');
      if (e is DioException) {
        debugPrint('DioException details:');
        debugPrint('Status Code: ${e.response?.statusCode}');
        debugPrint('Response Data: ${e.response?.data}');
        debugPrint('Request Data: ${e.requestOptions.data}');
        debugPrint('Headers: ${e.requestOptions.headers}');
      }
      rethrow;
    }
  }

  @override
  Future<void> addJournalEntry(
    String note,
    bool isSharedWithAI,
    String userId,
  ) async {
    try {
      debugPrint('API URL: $_baseUrl/add-journal-entry');
      debugPrint('Bearer Token: $_bearerToken');

      final response = await _dio.post(
        '$_baseUrl/add-journal-entry',
        options: Options(
          headers: {
            'Authorization': 'Bearer $_bearerToken',
            'Content-Type': 'application/json; charset=utf-8',
          },
          contentType: 'application/json',
        ),
        data: {"entry": note.toString(), "userId": userId.toString()},
      );

      debugPrint('API Response: ${response.statusCode} - ${response.data}');
    } catch (e) {
      debugPrint('Error adding journal entry to API: $e');
      if (e is DioException) {
        debugPrint('DioException details:');
        debugPrint('Status Code: ${e.response?.statusCode}');
        debugPrint('Response Data: ${e.response?.data}');
        debugPrint('Request Data: ${e.requestOptions.data}');
        debugPrint('Headers: ${e.requestOptions.headers}');
      }
      rethrow;
    }
  }

  @override
  Future<Response> sendToAIMessage(String question, String userId) async {
    try {
      debugPrint('API URL: $_baseUrl/send-message');
      debugPrint('Bearer Token: $_bearerToken');

      final response = await _dio.post(
        '$_baseUrl/ask-question-ai',
        options: Options(
          headers: {
            'Authorization': 'Bearer $_bearerToken',
            'Content-Type': 'application/json; charset=utf-8',
          },
          contentType: 'application/json',
        ),
        data: {"question": question.toString(), "userId": userId.toString()},
      );

      debugPrint('API Response: ${response.statusCode} - ${response.data}');

      return response;
    } catch (e) {
      debugPrint('Error sending message to API: $e');
      if (e is DioException) {
        debugPrint('DioException details:');
        debugPrint('Status Code: ${e.response?.statusCode}');
        debugPrint('Response Data: ${e.response?.data}');
        debugPrint('Request Data: ${e.requestOptions.data}');
        debugPrint('Headers: ${e.requestOptions.headers}');
      }
      rethrow;
    }
  }
}
